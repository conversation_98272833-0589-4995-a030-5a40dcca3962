import 'package:flutter/material.dart';

/// ### Все доступные шрифты приложения:
///
/// - Display (36 -> 40 -> 48)
/// - Headline (26 -> 28 -> 32)
/// - Title (20 -> 22 -> 24)
/// - Label (16 -> 18 -> 20)
/// - Body (14 -> 16 -> 18)
class Fonts {
  static const _percentOfLetterSpacing = 0.0;
  static const sansSerif = 'Inter';
  static const mono = 'JetBrainsMono';

  // DISPLAY
  /// **48PX | 1.4H | 500W**
  static const displayLarge = TextStyle(
    fontFamily: sansSerif,
    fontSize: 48.0,
    height: 1.4,
    letterSpacing: (_percentOfLetterSpacing / 100) * 48.0,
    fontVariations: [FontVariation.weight(500)],
  );

  /// **40PX | 1.4H | 500W**
  static const displayMedium = TextStyle(
    fontFamily: sansSerif,
    fontSize: 40.0,
    height: 1.4,
    letterSpacing: (_percentOfLetterSpacing / 100) * 40.0,
    fontVariations: [FontVariation.weight(500)],
  );

  /// **36PX | 1.4H | 500W**
  static const displaySmall = TextStyle(
    fontFamily: sansSerif,
    fontSize: 36.0,
    height: 1.4,
    letterSpacing: (_percentOfLetterSpacing / 100) * 36.0,
    fontVariations: [FontVariation.weight(500)],
  );

  // HEADLINE
  /// **32PX | 1.4H | 500W**
  static const headlineLarge = TextStyle(
    fontFamily: sansSerif,
    fontSize: 32.0,
    height: 1.4,
    letterSpacing: (_percentOfLetterSpacing / 100) * 32.0,
    fontVariations: [FontVariation.weight(500)],
  );

  /// **28PX | 1.4H | 500W**
  static const headlineMedium = TextStyle(
    fontFamily: sansSerif,
    fontSize: 28.0,
    height: 1.4,
    letterSpacing: (_percentOfLetterSpacing / 100) * 28.0,
    fontVariations: [FontVariation.weight(500)],
  );

  /// **26PX | 1.4H | 500W**
  static const headlineSmall = TextStyle(
    fontFamily: sansSerif,
    fontSize: 26.0,
    height: 1.4,
    letterSpacing: (_percentOfLetterSpacing / 100) * 26.0,
    fontVariations: [FontVariation.weight(500)],
  );

  // TITLE
  /// **24PX | 1.4H | 500W**
  static const titleLarge = TextStyle(
    fontFamily: sansSerif,
    fontSize: 24.0,
    height: 1.4,
    letterSpacing: (_percentOfLetterSpacing / 100) * 24.0,
    fontVariations: [FontVariation.weight(500)],
  );

  /// **22PX | 1.4H | 500W**
  static const titleMedium = TextStyle(
    fontFamily: sansSerif,
    fontSize: 22.0,
    height: 1.4,
    letterSpacing: (_percentOfLetterSpacing / 100) * 22.0,
    fontVariations: [FontVariation.weight(500)],
  );

  /// **20PX | 1.4H | 500W**
  static const titleSmall = TextStyle(
    fontFamily: sansSerif,
    fontSize: 20.0,
    height: 1.4,
    letterSpacing: (_percentOfLetterSpacing / 100) * 20.0,
    fontVariations: [FontVariation.weight(500)],
  );

  // LABEL
  /// **18PX | 1.2H | 500W**
  static const labelLarge = TextStyle(
    fontFamily: sansSerif,
    fontSize: 18.0,
    height: 1.2,
    letterSpacing: (_percentOfLetterSpacing / 100) * 20.0,
    fontVariations: [FontVariation.weight(500)],
  );

  /// **16PX | 1.2H | 500W**
  static const labelMedium = TextStyle(
    fontFamily: sansSerif,
    fontSize: 16.0,
    height: 1.2,
    letterSpacing: (_percentOfLetterSpacing / 100) * 18.0,
    fontVariations: [FontVariation.weight(500)],
  );

  /// **14PX | 1.4H | 500W**
  static const labelSmall = TextStyle(
    fontFamily: sansSerif,
    fontSize: 14.0,
    height: 1.4,
    letterSpacing: (_percentOfLetterSpacing / 100) * 16.0,
    fontVariations: [FontVariation.weight(500)],
  );

  // BODY
  /// **16PX | 1.4H | 400W**
  static const bodyLarge = TextStyle(
    fontFamily: sansSerif,
    fontSize: 16.0,
    height: 1.4,
    letterSpacing: (_percentOfLetterSpacing / 100) * 18.0,
    fontVariations: [FontVariation.weight(400)],
  );

  /// **15PX | 1.5H | 400W**
  static const bodyMedium = TextStyle(
    fontFamily: sansSerif,
    fontSize: 15.0,
    height: 1.5,
    letterSpacing: (_percentOfLetterSpacing / 100) * 16.0,
    fontVariations: [FontVariation.weight(400)],
  );

  /// **12PX | 1.5H | 400W**
  static const bodySmall = TextStyle(
    fontFamily: sansSerif,
    fontSize: 12.0,
    height: 1.5,
    letterSpacing: (_percentOfLetterSpacing / 100) * 14.0,
    fontVariations: [FontVariation.weight(400)],
  );
}
