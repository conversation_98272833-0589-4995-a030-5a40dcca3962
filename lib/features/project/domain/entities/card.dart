import 'dart:math';

import 'package:flutter/material.dart';
import 'package:sphere/core/gen/assets.gen.dart';
import 'package:sphere/features/project/data/models/project.dart';
import 'package:sphere/features/project/data/models/statuses.dart';
import 'package:sphere/features/user/presentation/user_card.dart';
import 'package:sphere/shared/styles/colors.dart';
import 'package:sphere/shared/styles/fonts.dart';
import 'package:sphere/shared/widgets/containers/card/index.dart';
import 'package:sphere/shared/widgets/svg/index.dart';
import 'package:sphere/shared/widgets/utility/info_list.dart';

class ProjectCard extends StatefulWidget {
  const ProjectCard({
    super.key,
    this.project,
    this.isOpened,
    this.onOpen,
    this.onSecondaryTapDown,
    this.onTap,
    this.isDesktop,
    this.isLoading,
  });

  final ProjectModel? project;
  final bool? isOpened;
  final void Function()? onOpen;
  final void Function()? onTap;
  final void Function(TapDownDetails)? onSecondaryTapDown;
  final bool? isDesktop;
  final bool? isLoading;

  @override
  State<ProjectCard> createState() => _ProjectCardState();
}

class _ProjectCardState extends State<ProjectCard>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();

    _controller = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 300),
    );

    _animation = Tween<double>(begin: 1, end: 0).animate(
      CurvedAnimation(parent: _controller, curve: Curves.linear),
    );

    if (widget.isOpened == true) {
      _controller.value = 1;
    }
  }

  @override
  void didUpdateWidget(ProjectCard oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.isOpened != oldWidget.isOpened) {
      if (widget.isOpened == true) {
        _controller.forward();
      } else {
        _controller.reverse();
      }
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Opacity(
      opacity: widget.project?.status != null &&
              widget.project!.status == ProjectStatus.archive
          ? 0.5
          : 1,
      child: CustomCard(
        isLoading: widget.isLoading ?? false,
        onTap: widget.onTap,
        onSecondaryTapDown: widget.onSecondaryTapDown,
        child: AnimatedSize(
          duration: const Duration(milliseconds: 200),
          alignment: Alignment.topCenter,
          curve: Curves.easeIn,
          child: Column(
            children: [
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          widget.project?.name ?? 'PROJECT NAME',
                          style: Fonts.labelLarge,
                        ),
                        Text(
                          widget.project?.number ?? 'DOCUMENT NAME',
                          style: Fonts.bodyMedium,
                        ),
                        if (widget.project?.status != null &&
                            widget.project!.status == ProjectStatus.archive)
                          Text(
                            widget.project?.status?.getName() ?? 'STATUS',
                            style: Fonts.bodySmall.merge(const TextStyle(
                                color: AppColors.lightDescription)),
                          ),
                      ],
                    ),
                  ),
                  if (widget.isDesktop != true)
                    IconButton(
                      onPressed: widget.onOpen,
                      icon: AnimatedBuilder(
                        animation: _animation,
                        builder: (context, child) {
                          return Transform.rotate(
                            angle: _animation.value * pi,
                            child: child,
                          );
                        },
                        child: SVG(Assets.icons.keyboardArrowUp),
                      ),
                    ),
                ],
              ),
              // const SizedBox(height: 12.0),
              // Row(mainAxisAlignment: MainAxisAlignment.spaceAround, children: [
              //   InfoBlock(
              //     icon: Assets.icons.weight,
              //     text: '${widget.project?.parameters?.mass ?? '?'} кг',
              //   ),
              //   InfoBlock(
              //     icon: Assets.icons.fitHeigth,
              //     text: '${widget.project?.parameters?.length ?? '?'} мм',
              //   ),
              //   InfoBlock(
              //     icon: Assets.icons.fitWidth,
              //     text: '${widget.project?.parameters?.width ?? '?'} мм',
              //   ),
              // ]),
              if (widget.project?.headUser != null) SizedBox(height: 12.0),
              if (widget.project?.headUser != null)
                Row(
                  children: [
                    Expanded(
                      child: UserCard(
                        user: widget.project!.headUser,
                        minified: true,
                        wrapped: false,
                      ),
                    ),
                  ],
                ),
              if (widget.isOpened == true || widget.isDesktop == true)
                const Divider(
                  height: 24.0,
                ),
              if (widget.isOpened == true || widget.isDesktop == true)
                InfoList(project: widget.project),
            ],
          ),
        ),
      ),
    );
  }
}
