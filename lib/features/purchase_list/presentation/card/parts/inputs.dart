import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:sphere/core/gen/assets.gen.dart';
import 'package:sphere/features/_initial/nomenclatures/data/models/material.dart';
import 'package:sphere/features/purchase_list/data/models/index.dart';
import 'package:sphere/features/purchase_list/data/models/provision.dart';
import 'package:sphere/features/purchase_list/data/models/supply.dart';
import 'package:sphere/features/purchase_list/data/repositories/index.dart';
import 'package:sphere/features/purchase_list/presentation/bloc/list/bloc.dart';
import 'package:sphere/features/purchase_list/presentation/card/parts/quantity.dart';
import 'package:sphere/features/purchase_list/presentation/create_lot.dart';
import 'package:sphere/shared/styles/colors.dart';
import 'package:sphere/shared/styles/fonts.dart';
import 'package:sphere/shared/widgets/containers/card/index.dart';
import 'package:sphere/shared/widgets/containers/popup/index.dart';
import 'package:sphere/shared/widgets/interactive/custom_drawer.dart';
import 'package:sphere/shared/widgets/interactive/dropdown.dart';
import 'package:sphere/shared/widgets/svg/index.dart';
import 'package:sphere/shared/widgets/utility/tooltip_span.dart';

class ProvisionProductsCard extends StatefulWidget {
  const ProvisionProductsCard({
    super.key,
    required this.data,
    required this.setInputsCoordinates,
    required this.refresher,
  });

  final ProvisionMaterialModel data;
  final void Function(Map<String, double> newInputsCoordinates)
      setInputsCoordinates;
  final void Function() refresher;

  @override
  State<ProvisionProductsCard> createState() => _ProvisionProductsCardState();
}

class _ProvisionProductsCardState extends State<ProvisionProductsCard>
    with TickerProviderStateMixin {
  final Map<int, GlobalKey> inputKeys = {};

  void _updateInputsCoordinates(BuildContext context) {
    final Map<String, double> coordinates = {};

    // Получаем RenderBox родительского контейнера
    final RenderBox? parentBox = context.findRenderObject() as RenderBox?;
    if (parentBox == null) {
      print('Parent RenderBox is null');
      return;
    }

    for (var index = 0; index < widget.data.products!.length; index++) {
      // print(index);
      // print('${widget.data.products![index]}');
      final key = inputKeys[index];
      if (key?.currentContext != null &&
          widget.data.products![index].id != null) {
        final RenderBox renderBox =
            key!.currentContext!.findRenderObject() as RenderBox;

        // Преобразуем глобальные координаты в локальные относительно родителя
        final offset = renderBox.localToGlobal(Offset.zero);
        final parentOffset = parentBox.localToGlobal(Offset.zero);
        final relativeY =
            offset.dy - parentOffset.dy + renderBox.size.height / 2;

        coordinates[widget.data.products![index].id!] = relativeY;

        // print('Input ${data.inputs![index].id!}: $relativeY');
      } else {
        print('Input key or id is null for index $index');
      }
    }

    print('input coordiantes: $coordinates');
    widget.setInputsCoordinates(coordinates);
  }

  void _deleteFromLot() {
    final selectedProducts =
        context.read<BlocPurchaseList>().state.selectedProducts;
    final List<String> selectedProductsIds = [];

    for (final product in selectedProducts.values) {
      if (product.id == null) return;
      selectedProductsIds.add(product.id!);
    }

    PurchaseListRepository.removeItems(provisionItemIds: selectedProductsIds);

    widget.refresher();
    context.read<BlocPurchaseList>().add(SetSelectedProducts({}));
  }

  void _showDetails(BuildContext context, ProvisionItemModel product) {
    CustomDropdownMenu.instance.hide();

    // final material = widget.data;
    // final adjustments

    showBaseDialog(context, builder: (context) {
      final isDarkTheme = Theme.of(context).brightness == Brightness.dark;

      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'История изменений (${product.adjustments?.length ?? 0})',
            style: Fonts.titleSmall,
          ),
          Expanded(
              child: Center(
            child: Text(
              'История изменений пуста...',
              style: Fonts.labelSmall.merge(TextStyle(
                  color: isDarkTheme
                      ? AppColors.darkDescription
                      : AppColors.lightDescription)),
            ),
          )),
          // Text(
          //   progressTask.status?.getName() ?? 'status not found',
          //   style: Fonts.labelSmall.merge(TextStyle(
          //     color: progressTask.status?.getColor(),
          //   )),
          // ),
          // SizedBox(height: 12.0),
          // UsersCard(
          //   minified: true,
          //   users: progressTask.workers ?? [],
          // ),
          // SizedBox(height: 12.0),
          // if (progressTask.comments == null || progressTask.comments!.isEmpty)
          //   Expanded(
          //     child: Center(
          //       child: Text(
          //         'Комментарии отсутствуют',
          //         style: Fonts.labelSmall.merge(
          //           TextStyle(
          //               color: isDarkTheme
          //                   ? AppColors.darkDescription
          //                   : AppColors.lightDescription),
          //         ),
          //       ),
          //     ),
          //   ),
          // if (progressTask.comments != null &&
          //     progressTask.comments!.isNotEmpty)
          //   Expanded(
          //     child: ListView.separated(
          //       reverse: true,
          //       itemCount: progressTask.comments?.length ?? 0,
          //       itemBuilder: (context, index) {
          //         final comment = progressTask.comments![index];

          //         return Comment(data: comment);
          //       },
          //       separatorBuilder: (_, __) => SizedBox(height: 8.0),
          //     ),
          //   )
        ],
      );
    });
  }

  @override
  Widget build(BuildContext context) {
    final state = context.read<BlocPurchaseList>().state;
    final isDarkTheme = Theme.of(context).brightness == Brightness.dark;
    final isCooperation =
        state.searchFilters?.filterType == ProvisionsFilter.cooperation;

    WidgetsBinding.instance.addPostFrameCallback((_) {
      _updateInputsCoordinates(context);
    });

    return Expanded(
      flex: 2,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        spacing: 4.0,
        children: [
          ...List.generate(widget.data.products!.length, (index) {
            final ProvisionItemModel product = widget.data.products![index];
            inputKeys[index] = GlobalKey();

            bool getChecked() {
              if (product.id != null) {
                if (state.selectedProducts.keys.contains(product.id)) {
                  return true;
                }
              } else if (state.selectedProducts.keys
                  .contains(product.uniqueId)) {
                return true;
              }

              return false;
            }

            return DragTarget<SupplyModel>(
              onAcceptWithDetails: (details) {
                // TODO: drop
                // final supply = details.data;
              },
              builder: (context, objects, list) {
                return Animate(
                  effects: [FadeEffect(duration: 200.ms)],
                  child: CustomCard(
                    onTap: () => _showDetails(context, product),
                    onSecondaryTapDown: (details) {
                      if (state.selectedProducts.isEmpty) return;
                      CustomDropdownMenu.instance.show(
                        context: context,
                        items: [
                          if (product.id == null)
                            CustomDropdownMenuItem(
                              icon: Assets.icons.add,
                              text: 'Создать лот',
                              onTap: () {
                                CustomDropdownMenu.instance.hide();
                                CustomDrawer.instance.show(
                                  context: context,
                                  vsync: this,
                                  child: CreateLotBody(
                                    refresher: widget.refresher,
                                  ),
                                );
                              },
                            ),
                          if (product.id != null)
                            CustomDropdownMenuItem(
                              icon: Assets.icons.delete,
                              text: 'Удалить из лота',
                              onTap: () {
                                CustomDropdownMenu.instance.hide();
                                showDialog(
                                  context: context,
                                  builder: (context) {
                                    return AlertDialog(
                                      title: const Text(
                                        'Удаление изделия',
                                        style: Fonts.titleSmall,
                                      ),
                                      content: Text(
                                        'Вы уверены удалить продукты из лота: "${state.selectedProducts.values.map((product) => product.product?.name).join('", "')}"',
                                        style: Fonts.bodyMedium,
                                      ),
                                      actionsAlignment:
                                          MainAxisAlignment.spaceAround,
                                      actions: [
                                        TextButton(
                                          onPressed: () {
                                            context.router.popForced();
                                          },
                                          child: const Text(
                                            'Отмена',
                                            style: Fonts.labelMedium,
                                          ),
                                        ),
                                        TextButton(
                                          // color: isDarkTheme
                                          //     ? AppColors.darkError
                                          //     : AppColors.lightError,
                                          onPressed: () {
                                            context.router.popForced();
                                            _deleteFromLot();
                                          },
                                          child: Text(
                                            'Да. удалить',
                                            style: Fonts.labelMedium.merge(
                                              TextStyle(
                                                color: isDarkTheme
                                                    ? AppColors.darkError
                                                    : AppColors.lightError,
                                              ),
                                            ),
                                          ),
                                        ),
                                      ],
                                    );
                                  },
                                );
                              },
                            ),
                        ],
                        position: details.globalPosition,
                      );
                    },
                    key: inputKeys[index],
                    padding: EdgeInsets.all(8.0),
                    child: Column(
                      children: [
                        Row(
                          children: [
                            SizedBox(
                                width: 24.0,
                                height: 24.0,
                                child: Checkbox.adaptive(
                                    value: getChecked(),
                                    onChanged: (_) {
                                      print(product);
                                      context
                                          .read<BlocPurchaseList>()
                                          .add(SelectProvisionProduct(product));
                                    })),
                            SizedBox(
                              width: 24.0,
                              height: 24.0,
                              child: Center(
                                child: Text(
                                  // TODO: change to priority
                                  product.product?.priority?.toString() ?? '0',
                                  style: Fonts.labelSmall.merge(
                                    TextStyle(
                                      fontFamily: Fonts.mono,
                                      color: isDarkTheme
                                          ? AppColors.darkSecondary
                                          : AppColors.lightSecondary,
                                    ),
                                  ),
                                ),
                              ),
                            ),
                            SizedBox(width: 12.0),
                            Expanded(
                              child: Text(
                                isCooperation
                                    ? product.featureType?.getName() ??
                                        'Product name not found (1)'
                                    : product.product?.name ??
                                        'Product name not found (2)',
                                style: Fonts.labelSmall,
                              ),
                            ),
                            SizedBox(width: 12.0),
                            if (product.product?.parameters
                                        ?.materialRequirements !=
                                    null &&
                                product.product!.parameters!
                                    .materialRequirements!.isNotEmpty)
                              Tooltip(
                                message:
                                    'Дополнительное требование к материалу:\n${product.product?.parameters!.materialRequirements ?? 'Requirements'}',
                                textStyle: Fonts.bodyMedium,
                                child: SVG(
                                  Assets.icons.help,
                                  color: isDarkTheme
                                      ? AppColors.darkWarning
                                      : AppColors.lightWarning,
                                ),
                              ),
                            if (product.product?.parameters
                                        ?.materialRequirements !=
                                    null &&
                                product.product!.parameters!
                                    .materialRequirements!.isNotEmpty)
                              SizedBox(width: 12.0),
                            PurchaseCardQuantity(
                              quantity: product.deliveryQuantity ?? 0.0,
                              totalQuantity: state.searchFilters?.filterType ==
                                      ProvisionsFilter.cooperation
                                  ? product.cooperationQuantity
                                  : product.totalQuantity,
                              unitType: state.searchFilters?.filterType ==
                                      ProvisionsFilter.cooperation
                                  ? UnitType.pcs
                                  : (widget.data.unitType ?? UnitType.kg),
                            ),
                          ],
                        ),
                        if (product.product?.parameters?.width != null ||
                            product.product?.parameters?.length != null ||
                            product.product?.parameters?.height != null)
                          SizedBox(height: 8.0),
                        if (product.product?.parameters?.width != null ||
                            product.product?.parameters?.length != null ||
                            product.product?.parameters?.height != null)
                          Row(
                              mainAxisAlignment: MainAxisAlignment.end,
                              children: [
                                RichText(
                                  text: TextSpan(
                                    style: Fonts.bodySmall.merge(TextStyle(
                                      height: 1.0,
                                      fontSize: 12.0,
                                      color: isDarkTheme
                                          ? AppColors.darkDescription
                                          : AppColors.lightDescription,
                                    )),
                                    children: [
                                      if (product.product?.parameters?.width !=
                                          null)
                                        TooltipSpan(
                                          message: 'Ширина, мм',
                                          inlineSpan: TextSpan(
                                            text: product
                                                .product?.parameters?.width
                                                .toString(),
                                            style:
                                                Fonts.bodySmall.merge(TextStyle(
                                              height: 1.0,
                                              fontSize: 12.0,
                                              color: isDarkTheme
                                                  ? AppColors.darkDescription
                                                  : AppColors.lightDescription,
                                            )),
                                          ),
                                        ),
                                      if (product.product?.parameters?.width !=
                                          null)
                                        TextSpan(text: ' / '),
                                      if (product.product?.parameters?.length !=
                                          null)
                                        TooltipSpan(
                                          message: 'Длина, мм',
                                          inlineSpan: TextSpan(
                                            text: product
                                                .product?.parameters?.length
                                                .toString(),
                                            style:
                                                Fonts.bodySmall.merge(TextStyle(
                                              height: 1.0,
                                              fontSize: 12.0,
                                              color: isDarkTheme
                                                  ? AppColors.darkDescription
                                                  : AppColors.lightDescription,
                                            )),
                                          ),
                                        ),
                                      if (product.product?.parameters?.length !=
                                          null)
                                        TextSpan(text: ' / '),
                                      if (product.product?.parameters?.height !=
                                          null)
                                        TooltipSpan(
                                          message: 'Высота (Диаметр), мм',
                                          inlineSpan: TextSpan(
                                            text: product
                                                .product?.parameters?.height
                                                .toString(),
                                            style:
                                                Fonts.bodySmall.merge(TextStyle(
                                              height: 1.0,
                                              fontSize: 12.0,
                                              color: isDarkTheme
                                                  ? AppColors.darkDescription
                                                  : AppColors.lightDescription,
                                            )),
                                          ),
                                        ),
                                    ],
                                  ),
                                )
                              ]),
                        // if (product.featureType != null) SizedBox(height: 8.0),
                        // if (product.featureType != null)
                        //   Row(
                        //     children: [
                        //       CustomChip(
                        //         text: product.featureType?.getName(),
                        //       ),
                        //     ],
                        //   ),
                        // if (product.product?.parameters?.featureTypes != null &&
                        //     product
                        //         .product!.parameters!.featureTypes!.isNotEmpty)
                        //   SizedBox(height: 8.0),
                        // if (product.product?.parameters?.featureTypes != null &&
                        //     product
                        //         .product!.parameters!.featureTypes!.isNotEmpty)
                        // Row(
                        //   children: [
                        //     Expanded(
                        //       child: Wrap(
                        //         spacing: 4.0,
                        //         runSpacing: 4.0,
                        //         children: [
                        //           ...product
                        //               .product!.parameters!.featureTypes!
                        //               .map((feature) {
                        //             return CustomChip(
                        //                 text: feature.getName());
                        //           })
                        //         ],
                        //       ),
                        //     ),
                        //   ],
                        // ),
                      ],
                    ),
                  ),
                );
              },
            );
          }),
        ],
      ),
    );
  }
}
