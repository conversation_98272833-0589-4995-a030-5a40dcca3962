import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:sphere/features/_initial/nomenclatures/data/models/material.dart';
import 'package:sphere/features/purchase_list/data/models/index.dart';
import 'package:sphere/features/purchase_list/data/models/provision.dart';
import 'package:sphere/features/purchase_list/data/repositories/index.dart';
import 'package:sphere/features/purchase_list/presentation/bloc/list/bloc.dart';
import 'package:sphere/shared/styles/colors.dart';
import 'package:sphere/shared/styles/fonts.dart';
import 'package:sphere/shared/widgets/interactive/elevated_button.dart';

@Deprecated("Use other")
class CreateLotBody extends StatefulWidget {
  const CreateLotBody({super.key, required this.refresher});

  final void Function() refresher;

  @override
  State<CreateLotBody> createState() => _CreateLotBodyState();
}

class _CreateLotBodyState extends State<CreateLotBody> {
  final _nameController = TextEditingController();
  bool _isLoading = false;

  void _createLot(Map<ProvisionItemModel, TextEditingController> data) async {
    setState(() {
      _isLoading = true;
    });
    final selectedFilter =
        context.read<BlocPurchaseList>().state.searchFilters?.filterType;

    await PurchaseListRepository.create(
      data: ProvisionsCreateModel(
        provisionName: _nameController.text,
        filterType: selectedFilter,
        items: data.keys.map(
          (product) {
            final controller = data[product];
            return ProvisionsCreateItemModel(
              productId: product.product?.id,
              featureType: product.featureType,
              quantity: double.tryParse(controller?.text ?? '0.0'),
            );
          },
        ).toList(),
      ),
    );
    widget.refresher();
    context.read<BlocPurchaseList>().add(SetSelectedProducts({}));

    setState(() {
      _isLoading = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    final isDarkTheme = Theme.of(context).brightness == Brightness.dark;

    return BlocBuilder<BlocPurchaseList, BlocPurchaseListState>(
        builder: (context, state) {
      final isCooperation =
          state.searchFilters?.filterType == ProvisionsFilter.cooperation;

      final selectedProducts = {
        for (var product in state.selectedProducts.values)
          product: TextEditingController(
            text: isCooperation
                ? product.cooperationQuantity.toString()
                : product.totalQuantity.toString(),
          ),
      };

      return Stack(
        children: [
          if (_isLoading)
            LinearProgressIndicator(
              minHeight: 2.0,
              borderRadius: BorderRadius.circular(20.0),
              color: isDarkTheme
                  ? AppColors.darkSecondary
                  : AppColors.lightSecondary,
            ),
          Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
            SizedBox(height: 12.0),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 12.0),
              child: Text(
                'Создание лота',
                style: Fonts.titleMedium.merge(
                  const TextStyle(height: 1.6),
                ),
              ),
            ),
            SizedBox(height: 12.0),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 12.0),
              child: TextField(
                controller: _nameController,
                style: Fonts.labelSmall,
                decoration: InputDecoration(
                  hintText: 'Название лота',
                ),
              ),
            ),
            Expanded(
              child: ListView.separated(
                padding: EdgeInsets.all(12.0),
                itemCount: selectedProducts.length,
                itemBuilder: (context, index) {
                  final product = selectedProducts.keys.toList()[index];
                  final controller = selectedProducts.values.toList()[index];

                  return CreateLotItem(
                    product: product,
                    controller: controller,
                    isCooperation: isCooperation,
                  );
                },
                separatorBuilder: (context, index) {
                  return SizedBox(height: 8.0);
                },
                // children: [
                //   ...selectedProducts.map((product) {
                //     return CreateLotItem(product: product);
                //   }),
                // ],
              ),
            ),
            Padding(
              padding: const EdgeInsets.all(12.0),
              child: CustomElevatedButton(
                type: CustomElevatedButtonTypes.accent,
                onPressed: () => _createLot(selectedProducts),
                text: 'Создать лот',
              ),
            ),
          ]),
        ],
      );
    });
  }
}

@Deprecated("")
class CreateLotItem extends StatelessWidget {
  const CreateLotItem({
    super.key,
    required this.product,
    required this.controller,
    required this.isCooperation,
  });

  final ProvisionItemModel product;
  final TextEditingController controller;
  final bool isCooperation;

  @override
  Widget build(BuildContext context) {
    return Row(children: [
      Expanded(
        child: Tooltip(
          message: product.product?.name,
          textStyle: Fonts.labelSmall,
          child: Text(
            product.product?.name ?? 'Product Name Not Found',
            style: Fonts.labelSmall,
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
        ),
      ),
      SizedBox(width: 8.0),
      Expanded(
        flex: 2,
        child: Row(children: [
          Expanded(
            child: TextField(
              controller: controller,
              style: Fonts.labelSmall,
              decoration: InputDecoration(
                hintText: 'Количество',
              ),
            ),
          ),
          SizedBox(width: 10.0),
          // TODO: change from hardcode
          Text(
            '/ ${isCooperation ? product.cooperationQuantity : product.totalQuantity?.toStringAsFixed(2)} ${isCooperation ? UnitType.pcs.getName() : (product.material?.baseUnit?.getName() ?? UnitType.kg.getName())}',
            style: Fonts.labelSmall,
          ),
        ]),
      ),
    ]);
  }
}
