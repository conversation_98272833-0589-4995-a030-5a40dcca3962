// import 'dart:html' as html;
import 'dart:io';

import 'package:auto_route/auto_route.dart';
import 'package:data_table_2/data_table_2.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:intl/intl.dart';
import 'package:path_provider/path_provider.dart';
import 'package:sphere/core/gen/assets.gen.dart';
import 'package:sphere/core/helpers/get_date_string.dart';
import 'package:sphere/features/product/data/models/product.dart';
import 'package:sphere/features/product/data/repositories/index.dart';
import 'package:sphere/features/project/data/models/departments.dart';
import 'package:sphere/features/purchase_list/data/models/index.dart';
import 'package:sphere/features/purchase_list/data/models/provision.dart';
import 'package:sphere/features/purchase_list/data/repositories/new.dart';
import 'package:sphere/features/purchase_list/domain/column.dart';
import 'package:sphere/features/purchase_list/presentation/batch_change_contract_status_dialog.dart';
import 'package:sphere/features/purchase_list/presentation/bloc/list/bloc.dart';
import 'package:sphere/features/purchase_list/presentation/change_contract_status_dialog.dart';
import 'package:sphere/features/purchase_list/presentation/column_visibility_dialog.dart';
import 'package:sphere/features/purchase_list/presentation/contract_edit_with_status_dialog.dart';
import 'package:sphere/features/purchase_list/presentation/create_contract.dart';
import 'package:sphere/features/purchase_list/presentation/filters/filter_panel.dart';
import 'package:sphere/features/tasks/data/models/index.dart';
import 'package:sphere/features/tasks/data/models/progress/task_progress.dart';
import 'package:sphere/features/tasks/data/models/task/task.dart';
import 'package:sphere/features/tasks/data/repositories/index.dart';
import 'package:sphere/features/tasks/presentation/add_supply_task.dart';
import 'package:sphere/features/user/presentation/user_card.dart';
import 'package:sphere/shared/data/models/search.dart';
import 'package:sphere/shared/styles/colors.dart';
import 'package:sphere/shared/styles/fonts.dart';
import 'package:sphere/shared/widgets/containers/popup/index.dart';
import 'package:sphere/shared/widgets/containers/wrapper/index.dart';
import 'package:sphere/shared/widgets/interactive/app_bar_features.dart';
import 'package:sphere/shared/widgets/interactive/chip.dart';
import 'package:sphere/shared/widgets/interactive/dropdown.dart';
import 'package:sphere/shared/widgets/interactive/elevated_button.dart';
import 'package:sphere/shared/widgets/interactive/ghost_button.dart';
import 'package:sphere/shared/widgets/overlay/app_bar/index.dart';
import 'package:sphere/shared/widgets/svg/index.dart';

@RoutePage()
class PurchaseListScreen extends StatefulWidget {
  const PurchaseListScreen({
    super.key,
    @PathParam('id') required this.id,
    required this.projectName,
  });

  final String id;
  final String projectName;

  @override
  State<PurchaseListScreen> createState() => _PurchaseListScreenState();
}

// Детальная информация по продукту в контракте, ProductDetail:
//
// productId String
// price number (>= 0)
// unit String
// deliveryDate DateTime
// quantity number (>= 0)
// notes String

class _PurchaseListScreenState extends State<PurchaseListScreen> {
  int _sortColumnIndex = 0;
  bool _sortAscending = true;

  // Task status change dialog state
  final TextEditingController _commentController = TextEditingController();

  void _showSnackBar(String message) {
    if (mounted) {
      ScaffoldMessenger.of(context)
          .showSnackBar(SnackBar(content: Text(message)));
    }
  }

  void _refreshData() {
    if (mounted) {
      context.read<BlocPurchaseList>().add(LoadDataWithFilters());
    }
  }

  Future<void> _updateNeedMaterial(
    ProductModel product,
    bool needMaterial,
  ) async {
    try {
      final parameters = product.parameters?.copyWith(
        needMaterial: needMaterial,
      );

      if (product.id == null || parameters == null) return;

      await ProductRepository.updateParameters(product.id!, parameters);
      _refreshData();
      _showSnackBar(needMaterial
          ? 'Материал отмечен как "наш"'
          : 'Отметка "наш материал" снята');
    } catch (e) {
      _showSnackBar('Ошибка при обновлении: $e');
    }
  }

  void _showCreateLotDialog() {
    final lotNameController = TextEditingController();
    DateTime? selectedDate;

    showBaseDialog(
      context,
      maxHeight: 420,
      maxWidth: 400,
      builder: (context) => StatefulBuilder(
        builder: (context, setDialogState) {
          final isDarkTheme = Theme.of(context).brightness == Brightness.dark;

          return Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text('Создать лот', style: Fonts.titleSmall),
              const SizedBox(height: 12.0),
              TextField(
                controller: lotNameController,
                decoration: const InputDecoration(
                  labelText: 'Название лота',
                  border: OutlineInputBorder(),
                ),
                style: Fonts.bodyMedium,
              ),
              const SizedBox(height: 16.0),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Expanded(
                    child: Text(
                      'Планируемая дата завершения тендера:',
                      style: Fonts.labelSmall,
                    ),
                  ),
                  SizedBox(width: 12.0),
                  GhostButton(
                    onTap: () async {
                      final date = await showDatePicker(
                        context: context,
                        initialDate: selectedDate ?? DateTime.now(),
                        firstDate: DateTime.now(),
                        lastDate: DateTime.now().add(Duration(days: 730)),
                      );
                      if (date != null) {
                        setDialogState(() {
                          selectedDate = date;
                        });
                      }
                    },
                    child: Text(
                      selectedDate != null
                          ? getDateString(selectedDate!)
                          : 'Выбрать дату',
                      style: Fonts.labelSmall.merge(
                        TextStyle(
                          color: isDarkTheme
                              ? AppColors.darkSecondary
                              : AppColors.lightSecondary,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
              const Spacer(),
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  TextButton(
                    onPressed: () => Navigator.of(context).pop(),
                    child: Text('Отмена', style: Fonts.bodyMedium),
                  ),
                  const SizedBox(width: 8.0),
                  CustomElevatedButton(
                    text: 'Создать',
                    onPressed: () async {
                      final lotName = lotNameController.text.trim();
                      final selectedProductIds = context
                          .read<BlocPurchaseList>()
                          .state
                          .selectedProducts
                          .keys
                          .toList();
                      if (lotName.isNotEmpty && selectedProductIds.isNotEmpty) {
                        final currentContext = context;
                        await PurchaseListRepositoryV2.createLot(
                          ProvisionCreateLotInput(
                            projectId: widget.id,
                            lotName: lotName,
                            productIds: selectedProductIds,
                            plannedTenderCompletionDate: selectedDate
                                ?.add(Duration(hours: 8))
                                .toIso8601String(),
                          ),
                        );
                        if (mounted && currentContext.mounted) {
                          Navigator.of(currentContext).pop();
                          _refreshData();
                          currentContext
                              .read<BlocPurchaseList>()
                              .add(ClearProvisionProductSelections());
                        }
                      }
                    },
                  ),
                ],
              ),
            ],
          );
        },
      ),
    );
  }

  void _showCreateContractDialog() {
    final selectedProductIds =
        context.read<BlocPurchaseList>().state.selectedProducts.keys.toList();
    showBaseDialog(
      context,
      maxWidth: 720,
      builder: (context) => CreateContractBody(
        projectId: widget.id,
        selectedProductIds: selectedProductIds,
        onContractCreated: () {
          _refreshData();
          context
              .read<BlocPurchaseList>()
              .add(ClearProvisionProductSelections());
        },
      ),
    );
  }

  void _showEditContractDialog(ContractModel contract) {
    // Используем productIds из контракта или выбранные продукты
    final selectedProductIds = contract.productIds ??
        context.read<BlocPurchaseList>().state.selectedProducts.keys.toList();

    showBaseDialog(
      context,
      maxWidth: 720,
      builder: (context) => CreateContractBody(
        projectId: widget.id,
        selectedProductIds: selectedProductIds,
        existingContract: contract,
        onContractCreated: () {
          _refreshData();
          context
              .read<BlocPurchaseList>()
              .add(ClearProvisionProductSelections());
        },
      ),
    );
  }

  void _showChangeContractStatusDialog(ContractModel contract) {
    showBaseDialog(
      context,
      maxWidth: 450,
      builder: (context) => ChangeContractStatusDialog(
        contract: contract,
        onStatusChanged: () {
          _refreshData();
        },
      ),
    );
  }

  void _showEditContractWithStatusDialog(ContractModel contract) {
    showBaseDialog(
      context,
      maxWidth: 800,
      maxHeight: 700,
      builder: (context) => ContractEditWithStatusDialog(
        contract: contract,
        projectId: widget.id,
        onContractUpdated: () {
          _refreshData();
          context
              .read<BlocPurchaseList>()
              .add(ClearProvisionProductSelections());
        },
      ),
    );
  }

  List<ContractModel> _getSelectedProductsWithContracts(
      BlocPurchaseListState state) {
    final selectedProductIds = state.selectedProducts.keys.toSet();
    final contracts = <ContractModel>[];

    for (final product in state.products) {
      if (selectedProductIds.contains(product.uniqueId) &&
          product.contract != null) {
        // Проверяем, что контракт еще не добавлен (избегаем дубликатов)
        if (!contracts.any((c) => c.id == product.contract!.id)) {
          contracts.add(product.contract!);
        }
      }
    }

    return contracts;
  }

  void _showContractStatusBatchDialog(List<ContractModel> contracts) {
    if (contracts.isEmpty) return;

    if (contracts.length == 1) {
      // Если только один контракт, показываем обычный диалог
      _showChangeContractStatusDialog(contracts.first);
    } else {
      // Если несколько контрактов, показываем диалог для массового изменения
      _showBatchContractStatusDialog(contracts);
    }
  }

  void _showBatchContractStatusDialog(List<ContractModel> contracts) {
    showBaseDialog(
      context,
      maxWidth: 500,
      builder: (context) => BatchChangeContractStatusDialog(
        contracts: contracts,
        onStatusChanged: () {
          _refreshData();
        },
      ),
    );
  }

  void _exportData(BlocPurchaseListState state) async {
    try {
      if (state.searchFilters == null) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Нет активных фильтров для экспорта')),
          );
        }
        return;
      }

      // Показываем индикатор загрузки
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Подготовка экспорта...')),
        );
      }

      // Выполняем запрос на экспорт
      final response = await PurchaseListRepositoryV2.export(
        widget.id,
        state.searchFilters!,
      );

      if (mounted) {
        if (response.statusCode == 200) {
          final Uint8List bytes = response.data as Uint8List;
          final filename =
              'export_${DateTime.now().millisecondsSinceEpoch}.zip';

          if (kIsWeb) {
            // // Web: Trigger browser download
            // final blob = html.Blob([bytes]);
            // final url = html.Url.createObjectUrlFromBlob(blob);
            // final anchor = html.AnchorElement(href: url)
            //   ..setAttribute('download', filename)
            //   ..click();
            // html.Url.revokeObjectUrl(url);
          } else {
            // Mobile: Save to file system
            final directory = await getDownloadsDirectory();
            final file = File('${directory?.path}/$filename');
            await file.writeAsBytes(bytes);
          }

          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Экспорт завершен успешно!'),
            ),
          );
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
                content: Text('Ошибка экспорта: ${response.statusMessage}')),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Ошибка при экспорте: $e')),
        );
      }
    }
  }

  void _showLotDialog({
    required String title,
    required String actionText,
    required Future<void> Function(List<int> lotNumbers) action,
  }) {
    final selectedLotNumbers = <int>[];
    showBaseDialog(
      context,
      maxHeight: 400,
      maxWidth: 400,
      builder: (dialogContext) => StatefulBuilder(
        builder: (context, setDialogState) => FutureBuilder(
          future: PurchaseListRepositoryV2.getColumnOptions(
            ProvisionsColumnOptionsInput(
              projectId: widget.id,
              column: ProvisionColumn.lotNumber,
              filterType: context
                  .read<BlocPurchaseList>()
                  .state
                  .searchFilters
                  ?.filterType,
            ),
          ),
          builder: (context, snapshot) {
            if (snapshot.connectionState == ConnectionState.waiting) {
              return const Center(child: CircularProgressIndicator.adaptive());
            }
            if (snapshot.hasError ||
                !snapshot.hasData ||
                snapshot.data!.data?.items == null) {
              return Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(title, style: Fonts.titleSmall),
                  const SizedBox(height: 12.0),
                  const Text('Ошибка загрузки лотов', style: Fonts.bodyMedium),
                  const SizedBox(height: 16.0),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      TextButton(
                        onPressed: () => Navigator.of(dialogContext).pop(),
                        child: Text('Закрыть', style: Fonts.bodyMedium),
                      ),
                    ],
                  ),
                ],
              );
            }
            final options = snapshot.data!.data!.items!;
            return Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(title, style: Fonts.titleSmall),
                const SizedBox(height: 12.0),
                Text('Выберите номера лота:', style: Fonts.bodyMedium),
                const SizedBox(height: 16.0),
                if (options.isEmpty)
                  const Padding(
                    padding: EdgeInsets.symmetric(vertical: 16.0),
                    child: Text('Нет доступных лотов', style: Fonts.bodyMedium),
                  )
                else
                  Expanded(
                    child: ListView.builder(
                      itemCount: options.length,
                      itemBuilder: (context, i) {
                        final lotNumber = int.tryParse(options[i]) ?? 0;
                        return CheckboxListTile(
                          title: Text(options[i]),
                          value: selectedLotNumbers.contains(lotNumber),
                          onChanged: (value) {
                            setDialogState(() {
                              if (value == true) {
                                selectedLotNumbers.add(lotNumber);
                              } else {
                                selectedLotNumbers.remove(lotNumber);
                              }
                            });
                          },
                        );
                      },
                    ),
                  ),
                const SizedBox(height: 16.0),
                Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    TextButton(
                      onPressed: () => Navigator.of(dialogContext).pop(),
                      child: Text('Отмена', style: Fonts.bodyMedium),
                    ),
                    const SizedBox(width: 8.0),
                    CustomElevatedButton(
                      text: actionText,
                      onPressed: () async {
                        if (selectedLotNumbers.isEmpty) return;

                        Navigator.of(dialogContext).pop();
                        await action(selectedLotNumbers);
                        if (mounted) {
                          context
                              .read<BlocPurchaseList>()
                              .add(ClearProvisionProductSelections());
                        }
                      },
                    ),
                  ],
                ),
              ],
            );
          },
        ),
      ),
    );
  }

  void _showCreateTaskDialog() {
    showBaseDialog(
      context,
      // maxHeight: 600,
      maxWidth: 720,
      builder: (context) => AddSupplyTaskDrawer(
        projectId: widget.id,
        onSuccess: () {
          Navigator.of(context).pop();
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(content: Text('Задача создана успешно!')),
            );
            // Refresh data if needed
            context.read<BlocPurchaseList>().add(LoadDataWithFilters());
          }
        },
        departments: [Department.osivk, Department.otk, Department.prd],
      ),
    );
  }

  void _showChangeStatusDialog(List<TaskProgressModel> selectedProgressTasks) {
    final isDarkTheme = Theme.of(context).brightness == Brightness.dark;
    TaskStatus newStatus = TaskStatus.inProgress;
    bool withChilds = false;

    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          backgroundColor: isDarkTheme
              ? AppColors.darkBackground
              : AppColors.lightBackground,
          title: const Text('Изменение статуса', style: Fonts.titleSmall),
          content: StatefulBuilder(builder: (context, setState) {
            return Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                ...TaskStatus.values.map((status) {
                  return RadioListTile<TaskStatus>(
                    value: status,
                    selected: status == newStatus,
                    title: Text(status.getName(), style: Fonts.labelSmall),
                    onChanged: (value) {
                      if (value != null) {
                        setState(() => newStatus = value);
                      }
                    },
                    groupValue: newStatus,
                  );
                }),
                const SizedBox(height: 12.0),
                TextField(
                  controller: _commentController,
                  style: Fonts.labelSmall,
                  decoration: const InputDecoration(
                    labelText: 'Комментарий',
                  ),
                  minLines: 1,
                  maxLines: 4,
                ),
                const SizedBox(height: 12.0),
                CheckboxListTile(
                  value: withChilds,
                  onChanged: (value) {
                    setState(() => withChilds = value ?? false);
                  },
                  title: const Text(
                    'Включить дочерние задачи',
                    style: Fonts.labelSmall,
                  ),
                ),
              ],
            );
          }),
          actions: [
            TextButton(
              onPressed: () {
                _commentController.clear();
                Navigator.of(context).pop();
              },
              child: const Text('Отмена'),
            ),
            CustomElevatedButton(
              type: CustomElevatedButtonTypes.accent,
              onPressed: () async {
                Navigator.of(context).pop();
                await _changeStatus(
                    selectedProgressTasks, newStatus, withChilds);
              },
              text: 'Изменить',
            ),
          ],
        );
      },
    );
  }

  Future<void> _changeStatus(List<TaskProgressModel> selectedProgressTasks,
      TaskStatus newStatus, bool withChilds) async {
    if (selectedProgressTasks.isEmpty) return;

    try {
      await TasksRepository.updateTaskProgressStatus(
        UpdateTaskProgressStatusModel(
          taskProgressIds: selectedProgressTasks
              .map((task) => task.id)
              .whereType<String>()
              .toList(),
          status: newStatus,
          comment: _commentController.text.isNotEmpty
              ? _commentController.text
              : null,
          includeChildren: withChilds,
        ),
      );

      _showSnackBar('Статус задач изменен!');
      _refreshData();
      _commentController.clear();
    } catch (e) {
      _showSnackBar('Ошибка при изменении статуса: $e');
    } finally {
      if (mounted) {
        context
            .read<BlocPurchaseList>()
            .add(ClearProgressTasksSelectionsInPurchaseList());
      }
    }
  }

  @override
  void initState() {
    super.initState();
    final bloc = context.read<BlocPurchaseList>();

    bloc.add(ApplyFilters(SearchFiltersModel(
      projectId: widget.id,
      filterType: ProvisionsFilter.cooperation,
    )));

    // if (bloc.state.searchFilters == null ||
    //     bloc.state.searchFilters?.filterType == null) {
    //   bloc.add(ApplyFilters(SearchFiltersModel(
    //     projectId: widget.id,
    //     filterType: ProvisionsFilter.cooperation,
    //   )));
    // }
    bloc.add(LoadDataWithFilters());
  }

  dynamic _getSortValue(ProvisionProductModel product, int columnIndex) {
    final p = product.product;
    final params = p?.parameters;

    switch (columnIndex) {
      case 0:
        return p?.createdAt ?? DateTime(0);
      case 1:
        return p?.designation ?? '';
      case 2:
        return p?.name ?? '';
      case 3:
        return product.material?.name ?? '';
      case 4:
        return params?.needMaterial ?? false;
      case 5:
        return product.features?.map((e) => e.getName()[0]).join(', ') ?? '';
      case 6:
        return params?.mass ?? 0.0;
      case 7:
        return params?.quantity ?? 0;
      case 8:
        return product.totalMass ?? 0.0;
      case 9:
        return params?.note ?? '';
      case 10:
        return params?.materialRequirements ?? '';
      case 13:
        return p?.priority ?? 0;
      case 16:
        return (p?.progressTasks ?? [])
            .map((task) => task.status?.getName() ?? '')
            .join(', ');
      case 17:
        final completedTasks = (p?.progressTasks ?? []).where((task) =>
            task.status == TaskStatus.completed && task.updatedAt != null);
        return completedTasks.isEmpty
            ? DateTime(0)
            : completedTasks
                .map((task) => task.updatedAt!)
                .reduce((a, b) => a.isAfter(b) ? a : b);
      case 20:
        return product.lots?.map((lot) => lot.lotNumber).join(', ') ?? '';
      case 21:
        return product.lots?.map((lot) => lot.lotName).join(', ') ?? '';
      case 22:
        return product.contract?.contractNumber ?? '';
      case 23:
        return product.contract?.supplier?.name ?? '';
      case 24:
        return product.contract?.plannedDeliveryDate ?? DateTime(0);
      case 25:
        return product.contract?.contractPrice ?? 0.0;
      default:
        return '';
    }
  }

  DataCell _cell(String? value, {bool selectable = false}) {
    final text = value ?? '–';
    final style = Fonts.bodySmall;
    return DataCell(selectable
        ? SelectableText(text, style: style)
        : Text(text, style: style));
  }

  DataCell _buildNeedMaterialCell(ProvisionProductModel product) {
    final needMaterial = product.product?.parameters?.needMaterial ?? false;
    return DataCell(
      Checkbox(
        value: needMaterial,
        onChanged: (value) async {
          if (value != null && product.product?.id != null) {
            await _updateNeedMaterial(product.product!, value);
          }
        },
      ),
    );
  }

  DataCell _buildContractCell(ProvisionProductModel product) {
    final contract = product.contract;
    final contractNumber = contract?.contractNumber ?? '–';
    final style = Fonts.bodySmall;

    if (contract != null) {
      return DataCell(
        GestureDetector(
          onTap: () => _showEditContractWithStatusDialog(contract),
          onSecondaryTapDown: (details) {
            CustomDropdownMenu.instance.show(
              context: context,
              position: details.globalPosition,
              items: [
                CustomDropdownMenuItem(
                  icon: Assets.icons.edit,
                  text: 'Редактировать контракт',
                  onTap: () {
                    CustomDropdownMenu.instance.hide();
                    _showEditContractWithStatusDialog(contract);
                  },
                ),
                CustomDropdownMenuItem(
                  icon: Assets.icons.repeat,
                  text: 'Изменить статус',
                  onTap: () {
                    CustomDropdownMenu.instance.hide();
                    _showChangeContractStatusDialog(contract);
                  },
                ),
              ],
            );
          },
          child: Container(
            padding: const EdgeInsets.symmetric(vertical: 4.0, horizontal: 8.0),
            decoration: BoxDecoration(
              color:
                  Theme.of(context).colorScheme.primary.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(4.0),
              border: Border.all(
                color: Theme.of(context)
                    .colorScheme
                    .primary
                    .withValues(alpha: 0.3),
              ),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  Icons.edit,
                  size: 14.0,
                  color: Theme.of(context).colorScheme.primary,
                ),
                const SizedBox(width: 4.0),
                Text(
                  contractNumber,
                  style: style.copyWith(
                    color: Theme.of(context).colorScheme.primary,
                    decoration: TextDecoration.underline,
                  ),
                ),
              ],
            ),
          ),
        ),
      );
    } else {
      return DataCell(Text(contractNumber, style: style));
    }
  }

  List<DataCell> _buildDataCells(
      ProvisionProductModel product, BlocPurchaseListState state) {
    final cells = [
      _cell(getDateString(product.product?.createdAt ?? DateTime(2000))),
      _cell(product.product?.number),
      _cell(product.product?.name),
      _cell(product.material?.name),
      _buildNeedMaterialCell(product), // Наш материал checkbox
      _cell(product.features?.map((e) => e.getName()[0]).join(', ')),
      _cell(product.product?.parameters?.mass?.toString()),
      _cell(product.totalQuantity?.toString()),
      _cell(product.totalMass.toString()),
      _cell(product.product?.parameters?.note),
      _cell(product.product?.parameters?.materialRequirements),
      _cell(product.parent?.name, selectable: true), // Узел
      _cell(product.parent?.number, selectable: true), // Род. сборки
      _cell(product.product?.priority?.toString()),
      DataCell(product.task?.workers?.first != null
          ? UserCard(
              minified: true,
              wrapped: false,
              user: product.task?.workers?.first)
          : Text('–', style: Fonts.bodySmall)), // Ответственный
      _cell(product.task?.createdAt != null
          ? DateFormat('dd.MM.yyyy').format(product.task!.createdAt!)
          : null), // Дата задачи
      DataCell(_buildTaskStatusCell(
          product.task, Fonts.bodySmall, product)), // Статус задачи
      DataCell(_buildTaskCompletionDateCell(
          product.task, Fonts.bodySmall)), // Плановая дата завершения
      _cell(product.status?.getName()), // Статус закупа
      _cell(product.lots
          ?.map((lot) => lot.plannedTenderCompletionDate != null
              ? DateFormat('dd.MM.yyyy')
                  .format(lot.plannedTenderCompletionDate!)
              : '–')
          .join(', ')
          .replaceAll('00:00:00.000', '')), // Плановая дата договора
      _cell(product.lots?.map((lot) => lot.lotNumber).join(', ')), // Номер лота
      _cell(
          product.lots?.map((lot) => lot.lotName).join(', ')), // Название лота
      _buildContractCell(product), // Номер договора
      _cell(product.contract?.supplier?.name), // Поставщик
      _cell(product.contract?.plannedDeliveryDate != null
          ? DateFormat('dd.MM.yyyy')
              .format(product.contract!.plannedDeliveryDate!)
          : null), // Дата поставки
      _cell(null), // Номер договора
      _cell(product.contract?.contractPrice?.toString()), // Стоимость
      _cell(null), // Примечание
      _cell(null), // ФИО Кладовщика
      _cell(null), // Количество на складе
      _cell(null), // Дата поступления
      _cell(null), // Сертификаты
      _cell(null), // Акт на брак
      // Task columns
      // DataCell(_buildTasksCell(product, fontStyle)), // Задачи
      // DataCell(_buildWorkersCell(product, fontStyle)), // Исполнители
      // DataCell(_buildTaskStatusCell(product, fontStyle)), // Статус задач
      // DataCell(_buildTaskDeadlineCell(product, fontStyle)), // Дедлайн задач
    ];
    return cells
        .asMap()
        .entries
        .where((e) => state.columnVisibility[e.key.toString()] ?? true)
        .map((e) => e.value)
        .toList();
  }

  Widget _buildTaskStatusCell(
    TaskProgressModel? task,
    TextStyle fontStyle,
    ProvisionProductModel product,
  ) {
    final status = task?.status;
    if (status == null) return Text('–', style: fontStyle);

    return BlocBuilder<BlocPurchaseList, BlocPurchaseListState>(
      builder: (context, state) {
        final isSelected = task != null &&
            state.selectedProgressTasks.any((t) => t.id == task.id);

        return GestureDetector(
          onTap: task != null
              ? () {
                  _showChangeStatusDialog([task]);
                }
              : null,
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 4.0, vertical: 1.0),
            decoration: BoxDecoration(
              color: status.getColor(),
              borderRadius: BorderRadius.circular(3.0),
              border: isSelected
                  ? Border.all(
                      color: Theme.of(context).colorScheme.primary,
                      width: 2.0,
                    )
                  : null,
            ),
            child: Text(
              status.getName(),
              style: fontStyle.copyWith(
                fontSize: 10.0,
                color: Colors.white,
                fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildTaskCompletionDateCell(
    TaskProgressModel? task,
    TextStyle fontStyle,
  ) {
    if (task?.deadlineDate == null) {
      return Text('–', style: fontStyle);
    }

    return Text(
      DateFormat('dd.MM.yyyy').format(task!.deadlineDate!),
      style: fontStyle,
    );
  }

  void _showColumnFilter(BuildContext context, int columnIndex) {
    showBaseDialog(
      context,
      builder: (context) => FilterPanel(
        projectId: widget.id,
        columnIndex: columnIndex,
      ),
      maxWidth: 600,
    );
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<BlocPurchaseList, BlocPurchaseListState>(
      builder: (context, state) {
        final visibleColumnIndexes = PurchaseListTableData.columns
            .asMap()
            .entries
            .where((e) => state.columnVisibility[e.key.toString()] ?? true)
            .map((e) => e.key)
            .toList();

        // Optimize: Use more efficient sorting with cached values
        final sortedProducts = List<ProvisionProductModel>.from(state.products)
          ..sort((a, b) {
            final aValue = _getSortValue(a, _sortColumnIndex);
            final bValue = _getSortValue(b, _sortColumnIndex);
            if (aValue is Comparable && bValue is Comparable) {
              return _sortAscending
                  ? aValue.compareTo(bValue)
                  : bValue.compareTo(aValue);
            }
            return 0;
          });

        return Wrapper(
          appBar: CustomAppBar(
            title: widget.projectName,
            description: 'Закупки',
            rightPart: Row(
              children: [
                if (state.selectedProvisionProductsForTask.isNotEmpty)
                  TextButton(
                    child: Row(
                      children: [
                        SVG(Assets.icons.close, width: 18.0),
                        const SizedBox(width: 8.0),
                        Text(
                          'Снять выделение (${state.selectedProvisionProductsForTask.length}) (${state.selectedProgressTasks.length})',
                          style: Fonts.labelSmall.merge(
                            const TextStyle(fontSize: 14.0),
                          ),
                        ),
                      ],
                    ),
                    onPressed: () {
                      context
                          .read<BlocPurchaseList>()
                          .add(ClearProvisionProductSelections());
                    },
                  ),
                IconButton(
                  onPressed: () => showColumnVisibilityDialog(context),
                  icon: SVG(Assets.icons.settings),
                ),
                const SizedBox(width: 16.0),
                CustomAppBarFeatures.getPopupMenu(
                  children: [
                    CustomDropdownMenuItem(
                      icon: Assets.icons.repeat,
                      text: 'Обновить',
                      onTap: () {
                        CustomDropdownMenu.instance.hide();
                        context
                            .read<BlocPurchaseList>()
                            .add(LoadDataWithFilters());
                      },
                    ),
                    CustomDropdownMenuItem(
                      icon: Assets.icons.description,
                      text: 'Экспорт в .zip',
                      onTap: () {
                        CustomDropdownMenu.instance.hide();
                        _exportData(state);
                      },
                    ),
                    if (state.selectedProvisionProductsForTask.isNotEmpty)
                      CustomDropdownMenuItem(
                        icon: Assets.icons.add,
                        text:
                            'Создать задачу (${state.selectedProvisionProductsForTask.length})',
                        onTap: () {
                          CustomDropdownMenu.instance.hide();
                          _showCreateTaskDialog();
                        },
                      ),
                    if (state.selectedProgressTasks.isNotEmpty)
                      CustomDropdownMenuItem(
                        icon: Assets.icons.edit,
                        text:
                            'Изменить статус задач (${state.selectedProgressTasks.length})',
                        onTap: () {
                          CustomDropdownMenu.instance.hide();
                          _showChangeStatusDialog(context
                              .read<BlocPurchaseList>()
                              .state
                              .selectedProgressTasks);
                        },
                      ),
                    if (state.selectedProducts.isNotEmpty) ...[
                      CustomDropdownMenuItem(
                        icon: Assets.icons.add,
                        text: 'Создать лот',
                        onTap: () {
                          CustomDropdownMenu.instance.hide();
                          _showCreateLotDialog();
                        },
                      ),
                      CustomDropdownMenuItem(
                        icon: Assets.icons.add,
                        text: 'Законтрактовать',
                        onTap: () {
                          CustomDropdownMenu.instance.hide();
                          _showCreateContractDialog();
                        },
                      ),
                      CustomDropdownMenuItem(
                        icon: Assets.icons.add,
                        text: 'Добавить в лот...',
                        onTap: () {
                          CustomDropdownMenu.instance.hide();
                          final selectedProductIds =
                              state.selectedProducts.keys.toList();
                          _showLotDialog(
                            title:
                                'Добавление в лот (${selectedProductIds.length})',
                            actionText: 'Добавить',
                            action: (lotNumbers) async {
                              await PurchaseListRepositoryV2.addToLot(
                                  ProvisionAddToLotInput(
                                productIds: selectedProductIds,
                                lotNumbers: lotNumbers,
                              ));
                              _refreshData();
                              _showSnackBar('Продукты добавлены в лот');
                            },
                          );
                        },
                      ),
                      CustomDropdownMenuItem(
                        icon: Assets.icons.delete,
                        text: 'Удалить из лотов...',
                        onTap: () {
                          CustomDropdownMenu.instance.hide();
                          final selectedProductIds =
                              state.selectedProducts.keys.toList();
                          _showLotDialog(
                            title:
                                'Удаление из лотов (${selectedProductIds.length})',
                            actionText: 'Удалить',
                            action: (lotNumbers) async {
                              await PurchaseListRepositoryV2.removeFromLot(
                                  ProvisionAddToLotInput(
                                productIds: selectedProductIds,
                                lotNumbers: lotNumbers,
                              ));
                              _refreshData();
                              _showSnackBar('Продукты удалены из лотов');
                            },
                          );
                        },
                      ),
                      if (_getSelectedProductsWithContracts(state).isNotEmpty)
                        CustomDropdownMenuItem(
                          icon: Assets.icons.repeat,
                          text:
                              'Изменить статус контрактов (${_getSelectedProductsWithContracts(state).length})',
                          onTap: () {
                            CustomDropdownMenu.instance.hide();
                            _showContractStatusBatchDialog(
                                _getSelectedProductsWithContracts(state));
                          },
                        ),
                    ],
                  ],
                  context: context,
                ),
              ],
            ),
          ),
          body: Stack(
            children: [
              Padding(
                padding: const EdgeInsets.only(top: 48.0),
                child: state.isLoading && state.products.isEmpty
                    ? const Center(child: CircularProgressIndicator.adaptive())
                    : state.error != null
                        ? Center(
                            child: Column(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Text('Ошибка: ${state.error}',
                                    style: Fonts.bodyMedium),
                                const SizedBox(height: 16),
                                CustomElevatedButton(
                                  text: 'Повторить',
                                  onPressed: () => context
                                      .read<BlocPurchaseList>()
                                      .add(LoadDataWithFilters()),
                                ),
                              ],
                            ),
                          )
                        : DataTable2(
                            minWidth: visibleColumnIndexes.fold(
                                100.0,
                                (sum, i) =>
                                    sum! +
                                    (PurchaseListTableData.columns[i].width ??
                                        0.0)),
                            dividerThickness: 1.0,
                            sortArrowBuilder: (ascending, sorted) => sorted
                                ? SVG(ascending
                                    ? Assets.icons.keyboardArrowUp
                                    : Assets.icons.keyboardArrowDown)
                                : const SizedBox(),
                            sortColumnIndex: _sortColumnIndex,
                            sortAscending: _sortAscending,
                            showCheckboxColumn: true,
                            showBottomBorder: true,
                            columnSpacing: 8,
                            columns: visibleColumnIndexes
                                .map((i) => DataColumn2(
                                      onSort: (index, ascending) {
                                        setState(() {
                                          final originalIndex =
                                              visibleColumnIndexes[index];
                                          _sortColumnIndex = originalIndex;
                                          _sortAscending = ascending;
                                        });
                                      },
                                      label: Row(
                                        children: [
                                          Expanded(
                                              child: Text(
                                                  PurchaseListTableData
                                                          .columns[i].name ??
                                                      '–',
                                                  style: Fonts.labelSmall)),
                                          IconButton(
                                            icon: Icon(Icons.filter_alt,
                                                size: 16,
                                                color: Theme.of(context)
                                                    .iconTheme
                                                    .color),
                                            onPressed: () =>
                                                _showColumnFilter(context, i),
                                          ),
                                        ],
                                      ),
                                      tooltip: PurchaseListTableData
                                          .columns[i].description,
                                      fixedWidth: PurchaseListTableData
                                          .columns[i].width,
                                    ))
                                .toList(),
                            rows: sortedProducts.map((product) {
                              final id = product.product?.id;
                              final withLot = product.lots?.isNotEmpty ?? false;
                              return DataRow2(
                                color: WidgetStateColor.resolveWith((states) {
                                  final alpha =
                                      states.contains(WidgetState.selected)
                                          ? 0.22
                                          : states.contains(WidgetState.hovered)
                                              ? 0.18
                                              : withLot
                                                  ? 0.11
                                                  : 0.0;
                                  return Theme.of(context)
                                      .colorScheme
                                      .primary
                                      .withValues(
                                          alpha: alpha,
                                          blue: withLot ? 0.33 : 0);
                                }),
                                onSelectChanged: id != null
                                    ? (value) {
                                        context.read<BlocPurchaseList>().add(
                                              ToggleProvisionProductSelection(
                                                  product),
                                            );
                                      }
                                    : null,
                                selected:
                                    state.selectedProducts.containsKey(id),
                                cells: _buildDataCells(product, state),
                              );
                            }).toList(),
                          ),
              ),
              Align(
                alignment: AlignmentDirectional.topStart,
                child: Padding(
                  padding: const EdgeInsets.fromLTRB(12.0, 12.0, 12.0, 0.0),
                  child: Row(
                    children: [
                      Expanded(
                        child: Wrap(
                          spacing: 4.0,
                          runSpacing: 4.0,
                          children: ProvisionsFilter.values
                              .map((filter) => CustomChip(
                                    selected: filter ==
                                        state.searchFilters?.filterType,
                                    onTap: () {
                                      final bloc =
                                          context.read<BlocPurchaseList>();
                                      if (bloc.state.searchFilters == null) {
                                        return;
                                      }
                                      bloc.add(ApplyFilters(
                                          bloc.state.searchFilters!.copyWith(
                                        filterType: filter ==
                                                state.searchFilters?.filterType
                                            ? null
                                            : filter,
                                      )));
                                    },
                                    text: filter.getName(),
                                  ))
                              .toList(),
                        ),
                      ),
                      if (state.searchFilters?.hasActiveFilters ?? false)
                        Expanded(
                          child: Wrap(
                            crossAxisAlignment: WrapCrossAlignment.center,
                            alignment: WrapAlignment.end,
                            spacing: 4.0,
                            runSpacing: 4.0,
                            children: [
                              if (state.isLoading)
                                const CircularProgressIndicator.adaptive(
                                  backgroundColor: AppColors.lightSecondary,
                                ),
                              if (state.isLoading) const SizedBox(width: 4.0),
                              GhostButton(
                                color: AppColors.lightSecondary,
                                onTap: () => context
                                    .read<BlocPurchaseList>()
                                    .add(ClearFilters()),
                                child: Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    const Icon(
                                      Icons.repeat_rounded,
                                      color: AppColors.lightSecondary,
                                    ),
                                    const SizedBox(width: 4.0),
                                    Text(
                                      'Сбросить все фильтры',
                                      style: Fonts.labelSmall.copyWith(
                                        color: AppColors.lightSecondary,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}
