import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:sphere/features/_initial/nomenclatures/data/models/material.dart';
import 'package:sphere/features/purchase_list/data/models/index.dart';
import 'package:sphere/features/purchase_list/data/repositories/new.dart';
import 'package:sphere/shared/widgets/containers/card/index.dart';

class ProductDetailsSectionV2 extends StatefulWidget {
  const ProductDetailsSectionV2({
    super.key,
    required this.selectedProductIds,
    required this.productDetails,
    required this.onProductDetailsChanged,
  });

  final List<String> selectedProductIds;
  final List<ProductDetail> productDetails;
  final ValueChanged<List<ProductDetail>> onProductDetailsChanged;

  @override
  State<ProductDetailsSectionV2> createState() =>
      _ProductDetailsSectionV2State();
}

class _ProductDetailsSectionV2State extends State<ProductDetailsSectionV2> {
  late List<ProductDetail> _productDetails;
  List<Warehouse> _warehouses = [];
  List<Warehouse> _filteredWarehouses = [];
  bool _warehousesLoading = false;
  final Map<String, TextEditingController> _warehouseSearchControllers = {};
  final Map<String, TextEditingController> _deliveryDateControllers = {};

  @override
  void initState() {
    super.initState();
    // Отложенная инициализация после завершения построения
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _initializeProductDetails();
      _loadWarehouses();
    });
  }

  @override
  void didUpdateWidget(ProductDetailsSectionV2 oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.selectedProductIds != widget.selectedProductIds ||
        oldWidget.productDetails != widget.productDetails) {
      // Отложенная инициализация после завершения построения
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _initializeProductDetails();
      });
    }
  }

  void _initializeProductDetails() {
    _productDetails = List<ProductDetail>.from(widget.productDetails);

    bool hasChanges = false;

    // Создаем записи для всех выбранных продуктов, если их еще нет
    for (final productId in widget.selectedProductIds) {
      if (!_productDetails.any((detail) => detail.productId == productId)) {
        _productDetails.add(ProductDetail(
          productId: productId,
          price: 0.0,
          unit: UnitType.kg,
          distributions: [
            Distribution(
              warehouseId: null,
              quantity: 1.0,
              deliveryDate: null,
              notes: null,
            ),
          ],
          notes: null,
        ));
        hasChanges = true;
      }
    }

    // Удаляем записи для продуктов, которые больше не выбраны
    final initialLength = _productDetails.length;
    _productDetails.removeWhere(
      (detail) => !widget.selectedProductIds.contains(detail.productId),
    );
    if (_productDetails.length != initialLength) {
      hasChanges = true;
    }

    // Обновляем контроллеры для дат поставки
    _updateDeliveryDateControllers();

    // Отложенное уведомление об изменениях после завершения построения
    if (hasChanges) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _notifyChange();
      });
    }
  }

  void _updateDeliveryDateControllers() {
    for (int productIndex = 0;
        productIndex < _productDetails.length;
        productIndex++) {
      final productDetail = _productDetails[productIndex];
      final distributions = productDetail.distributions ?? [];
      for (int distributionIndex = 0;
          distributionIndex < distributions.length;
          distributionIndex++) {
        final distributionKey =
            _getDistributionKey(productIndex, distributionIndex);
        final controller = _getDeliveryDateController(distributionKey);
        final deliveryDate =
            distributions[distributionIndex].deliveryDate ?? '';
        if (controller.text != deliveryDate) {
          controller.text = deliveryDate;
        }
      }
    }
  }

  void _notifyChange() {
    if (mounted) {
      widget.onProductDetailsChanged(_productDetails);
    }
  }

  void _updateProductDetail(int index, ProductDetail updatedDetail) {
    if (mounted && index >= 0 && index < _productDetails.length) {
      setState(() {
        _productDetails[index] = updatedDetail;
        _updateDeliveryDateControllers();
      });
      _notifyChange();
    }
  }

  Future<void> _loadWarehouses() async {
    if (!mounted) return;

    setState(() {
      _warehousesLoading = true;
    });

    try {
      final response = await PurchaseListRepositoryV2.searchWarehouses(
        includeContractors: true,
      );

      if (mounted && response.data != null) {
        setState(() {
          final warehouses = response.data!.warehouses ?? [];
          debugPrint('Загружено складов: ${warehouses.length}');
          _warehouses = warehouses;
          _filteredWarehouses = List<Warehouse>.from(warehouses);
        });
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Ошибка загрузки складов: $e')),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _warehousesLoading = false;
        });
      }
    }
  }

  void _filterWarehouses(String query, String distributionKey) {
    setState(() {
      if (query.isEmpty) {
        _filteredWarehouses = List<Warehouse>.from(_warehouses);
      } else {
        _filteredWarehouses = _warehouses
            .where((warehouse) =>
                warehouse.name.toLowerCase().contains(query.toLowerCase()) ||
                (warehouse.address
                        ?.toLowerCase()
                        .contains(query.toLowerCase()) ??
                    false) ||
                (warehouse.description
                        ?.toLowerCase()
                        .contains(query.toLowerCase()) ??
                    false))
            .toList();
      }
      debugPrint(
        'Поиск складов: "$query", найдено: ${_filteredWarehouses.length} из ${_warehouses.length}',
      );
    });
  }

  String _getDistributionKey(int productIndex, int distributionIndex) {
    return '${productIndex}_$distributionIndex';
  }

  TextEditingController _getSearchController(String distributionKey) {
    if (!_warehouseSearchControllers.containsKey(distributionKey)) {
      _warehouseSearchControllers[distributionKey] = TextEditingController();
    }
    return _warehouseSearchControllers[distributionKey]!;
  }

  TextEditingController _getDeliveryDateController(String distributionKey) {
    if (!_deliveryDateControllers.containsKey(distributionKey)) {
      _deliveryDateControllers[distributionKey] = TextEditingController();
    }
    return _deliveryDateControllers[distributionKey]!;
  }

  @override
  void dispose() {
    for (final controller in _warehouseSearchControllers.values) {
      controller.dispose();
    }
    for (final controller in _deliveryDateControllers.values) {
      controller.dispose();
    }
    super.dispose();
  }

  Future<void> _pickDate(
    BuildContext context,
    int productIndex,
    int distributionIndex,
  ) async {
    if (!mounted ||
        productIndex < 0 ||
        productIndex >= _productDetails.length) {
      return;
    }

    final productDetail = _productDetails[productIndex];
    if (productDetail.distributions == null ||
        distributionIndex < 0 ||
        distributionIndex >= productDetail.distributions!.length) {
      return;
    }

    final DateTime? pickedDate = await showDatePicker(
      context: context,
      initialDate: DateTime.now(),
      firstDate: DateTime.now(),
      lastDate: DateTime(2100),
    );

    if (pickedDate != null && mounted) {
      final formattedDate =
          '${pickedDate.year}-${pickedDate.month.toString().padLeft(2, '0')}-${pickedDate.day.toString().padLeft(2, '0')}';

      final updatedDistributions =
          List<Distribution>.from(productDetail.distributions!);
      updatedDistributions[distributionIndex] =
          updatedDistributions[distributionIndex]
              .copyWith(deliveryDate: formattedDate);

      final distributionKey =
          _getDistributionKey(productIndex, distributionIndex);
      final controller = _getDeliveryDateController(distributionKey);
      controller.text = formattedDate;

      _updateProductDetail(
        productIndex,
        productDetail.copyWith(distributions: updatedDistributions),
      );
    }
  }

  void _addDistribution(int productIndex) {
    if (productIndex < 0 || productIndex >= _productDetails.length) return;

    final productDetail = _productDetails[productIndex];
    final currentDistributions = productDetail.distributions ?? [];

    final updatedDistributions = List<Distribution>.from(currentDistributions)
      ..add(Distribution(
        warehouseId: null,
        quantity: 1.0,
        deliveryDate: null,
        notes: null,
      ));

    _updateProductDetail(
      productIndex,
      productDetail.copyWith(distributions: updatedDistributions),
    );
  }

  void _removeDistribution(int productIndex, int distributionIndex) {
    if (productIndex < 0 || productIndex >= _productDetails.length) return;

    final productDetail = _productDetails[productIndex];
    if (productDetail.distributions == null ||
        distributionIndex < 0 ||
        distributionIndex >= productDetail.distributions!.length) {
      return;
    }

    if (productDetail.distributions!.length <= 1) {
      return;
    }

    final updatedDistributions =
        List<Distribution>.from(productDetail.distributions!)
          ..removeAt(distributionIndex);

    final distributionKey =
        _getDistributionKey(productIndex, distributionIndex);
    _deliveryDateControllers[distributionKey]?.dispose();
    _deliveryDateControllers.remove(distributionKey);

    _updateProductDetail(
      productIndex,
      productDetail.copyWith(distributions: updatedDistributions),
    );
  }

  @override
  Widget build(BuildContext context) {
    if (_productDetails.isEmpty) {
      return const SizedBox.shrink();
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Детали продуктов',
          style: Theme.of(context).textTheme.headlineSmall,
        ),
        const SizedBox(height: 16.0),
        if (_warehousesLoading)
          const Center(child: CircularProgressIndicator())
        else
          ..._productDetails.asMap().entries.map((entry) {
            final index = entry.key;
            final detail = entry.value;
            return _buildProductDetailCard(index, detail);
          }),
      ],
    );
  }

  Widget _buildProductDetailCard(int productIndex, ProductDetail detail) {
    print(detail);

    return CustomCard(
      margin: const EdgeInsets.only(bottom: 16.0),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Expanded(
                  child: Text(
                    '${detail.productName ?? detail.productId}',
                    style: Theme.of(context).textTheme.titleMedium,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16.0),
            _buildProductBasicInfo(productIndex, detail),
            const SizedBox(height: 16.0),
            _buildDistributionsSection(productIndex, detail),
          ],
        ),
      ),
    );
  }

  Widget _buildProductBasicInfo(int productIndex, ProductDetail detail) {
    return Column(
      children: [
        Row(
          children: [
            Expanded(
              child: TextFormField(
                initialValue: detail.price?.toString() ?? '',
                decoration: const InputDecoration(
                  labelText: 'Цена *',
                  border: OutlineInputBorder(),
                  suffixText: '₽',
                  hintText: '0.0',
                ),
                keyboardType: TextInputType.number,
                inputFormatters: [
                  FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d*')),
                ],
                onChanged: (value) {
                  final price = double.tryParse(value) ?? 0.0;
                  _updateProductDetail(
                    productIndex,
                    detail.copyWith(price: price),
                  );
                },
              ),
            ),
            const SizedBox(width: 12.0),
            Expanded(
              child: DropdownButtonFormField<UnitType>(
                value: _productDetails[productIndex].unit,
                decoration: const InputDecoration(
                  labelText: 'Единица измерения',
                  border: OutlineInputBorder(),
                ),
                items: [
                  ...UnitType.values.map((unit) {
                    return DropdownMenuItem(
                      value: unit,
                      child: Text(unit.getName()),
                    );
                  })
                ],
                onChanged: (value) {
                  _updateProductDetail(
                    productIndex,
                    detail.copyWith(unit: value),
                  );
                },
              ),
            ),
          ],
        ),
        const SizedBox(height: 12.0),
        TextFormField(
          initialValue: detail.notes ?? '',
          decoration: const InputDecoration(
            labelText: 'Примечания к продукту',
            border: OutlineInputBorder(),
          ),
          maxLines: 2,
          onChanged: (value) {
            _updateProductDetail(
              productIndex,
              detail.copyWith(notes: value.isEmpty ? null : value),
            );
          },
        ),
      ],
    );
  }

  Widget _buildDistributionsSection(int productIndex, ProductDetail detail) {
    final distributions = detail.distributions ?? [];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Text(
              'Распределения по складам',
              style: Theme.of(context).textTheme.titleSmall,
            ),
            const Spacer(),
            IconButton(
              onPressed: () => _addDistribution(productIndex),
              icon: const Icon(Icons.add),
              tooltip: 'Добавить распределение',
            ),
          ],
        ),
        const SizedBox(height: 8.0),
        if (distributions.isEmpty)
          const Text('Нет распределений')
        else
          ...distributions.asMap().entries.map((entry) {
            final distributionIndex = entry.key;
            final distribution = entry.value;
            return _buildDistributionCard(
              productIndex,
              distributionIndex,
              distribution,
            );
          }),
      ],
    );
  }

  Widget _buildDistributionCard(
    int productIndex,
    int distributionIndex,
    Distribution distribution,
  ) {
    return CustomCard(
      color: Colors.grey[50],
      margin: const EdgeInsets.only(bottom: 8.0),
      child: Padding(
        padding: const EdgeInsets.all(12.0),
        child: Column(
          children: [
            Row(
              children: [
                Text(
                  'Распределение ${distributionIndex + 1}',
                  style: Theme.of(context)
                      .textTheme
                      .bodyMedium
                      ?.copyWith(fontWeight: FontWeight.w600),
                ),
                const Spacer(),
                if (_productDetails[productIndex].distributions!.length > 1)
                  IconButton(
                    onPressed: () =>
                        _removeDistribution(productIndex, distributionIndex),
                    icon: const Icon(Icons.delete, size: 20),
                    tooltip: 'Удалить распределение',
                  ),
              ],
            ),
            const SizedBox(height: 12.0),
            _buildDistributionFields(
                productIndex, distributionIndex, distribution),
          ],
        ),
      ),
    );
  }

  Widget _buildDistributionFields(
    int productIndex,
    int distributionIndex,
    Distribution distribution,
  ) {
    final distributionKey =
        _getDistributionKey(productIndex, distributionIndex);
    final deliveryDateController = _getDeliveryDateController(distributionKey);

    if (deliveryDateController.text != (distribution.deliveryDate ?? '')) {
      deliveryDateController.text = distribution.deliveryDate ?? '';
    }

    return Column(
      children: [
        Row(
          children: [
            Expanded(
              child: _buildWarehouseSelector(
                productIndex,
                distributionIndex,
                distribution,
              ),
            ),
            const SizedBox(width: 12.0),
            Tooltip(
              message: 'Установить для всех',
              child: IconButton(
                onPressed: () {
                  if (distribution.warehouseId == null) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(content: Text('Сначала выберите склад')),
                    );
                    return;
                  }

                  final updatedProductDetails =
                      _productDetails.asMap().entries.map((entry) {
                    // final index = entry.key;
                    final detail = entry.value;

                    final updatedDistributions =
                        detail.distributions?.map((dist) {
                              return dist.copyWith(
                                  warehouseId: distribution.warehouseId);
                            }).toList() ??
                            [];

                    return detail.copyWith(distributions: updatedDistributions);
                  }).toList();

                  setState(() {
                    _productDetails = updatedProductDetails;
                    _updateDeliveryDateControllers();
                  });
                  _notifyChange();

                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                        content: Text('Склад установлен для всех продуктов')),
                  );
                },
                icon: Icon(CupertinoIcons.repeat),
              ),
            ),
            const SizedBox(width: 12.0),
            Expanded(
              child: TextFormField(
                initialValue: distribution.quantity?.toString() ?? '',
                decoration: const InputDecoration(
                  labelText: 'Количество *',
                  border: OutlineInputBorder(),
                ),
                keyboardType: TextInputType.number,
                inputFormatters: [
                  FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d*')),
                ],
                onChanged: (value) {
                  final quantity = double.tryParse(value);
                  _updateDistribution(
                    productIndex,
                    distributionIndex,
                    distribution.copyWith(quantity: quantity),
                  );
                },
              ),
            ),
          ],
        ),
        const SizedBox(height: 12.0),
        Row(
          children: [
            Expanded(
              child: TextFormField(
                controller: deliveryDateController,
                decoration: InputDecoration(
                  labelText: 'Дата поставки',
                  border: const OutlineInputBorder(),
                  suffixIcon: IconButton(
                    icon: const Icon(Icons.calendar_today),
                    onPressed: () =>
                        _pickDate(context, productIndex, distributionIndex),
                  ),
                ),
                readOnly: true,
              ),
            ),
          ],
        ),
        const SizedBox(height: 12.0),
        TextFormField(
          initialValue: distribution.notes ?? '',
          decoration: const InputDecoration(
            labelText: 'Примечания к распределению',
            border: OutlineInputBorder(),
          ),
          maxLines: 2,
          onChanged: (value) {
            _updateDistribution(
              productIndex,
              distributionIndex,
              distribution.copyWith(notes: value.isEmpty ? null : value),
            );
          },
        ),
      ],
    );
  }

  void _updateDistribution(int productIndex, int distributionIndex,
      Distribution updatedDistribution) {
    if (productIndex < 0 || productIndex >= _productDetails.length) return;

    final productDetail = _productDetails[productIndex];
    if (productDetail.distributions == null ||
        distributionIndex < 0 ||
        distributionIndex >= productDetail.distributions!.length) {
      return;
    }

    final updatedDistributions =
        List<Distribution>.from(productDetail.distributions!);
    updatedDistributions[distributionIndex] = updatedDistribution;

    final distributionKey =
        _getDistributionKey(productIndex, distributionIndex);
    final controller = _getDeliveryDateController(distributionKey);
    controller.text = updatedDistribution.deliveryDate ?? '';

    _updateProductDetail(
      productIndex,
      productDetail.copyWith(distributions: updatedDistributions),
    );
  }

  Future<void> _showWarehouseSearchDialog(
    BuildContext context,
    int productIndex,
    int distributionIndex,
    Distribution distribution,
    String distributionKey,
  ) async {
    final controller = _getSearchController(distributionKey);
    controller.clear();
    _filterWarehouses('', distributionKey);

    final result = await showDialog<Warehouse?>(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setDialogState) => AlertDialog(
          title: const Text('Выбор склада'),
          content: SizedBox(
            width: 400,
            height: 500,
            child: Column(
              children: [
                TextField(
                  controller: controller,
                  decoration: const InputDecoration(
                    hintText: 'Поиск складов...',
                    prefixIcon: Icon(Icons.search),
                    border: OutlineInputBorder(),
                  ),
                  onChanged: (query) {
                    _filterWarehouses(query, distributionKey);
                    setDialogState(() {});
                  },
                ),
                const SizedBox(height: 16),
                Expanded(
                  child: ListView.builder(
                    itemCount: _filteredWarehouses.length +
                        2, // +2 for "Не выбран" and divider
                    itemBuilder: (context, index) {
                      if (index == 0) {
                        return ListTile(
                          leading: const Icon(Icons.clear),
                          title: const Text('Не выбран'),
                          onTap: () => Navigator.of(context).pop(null),
                        );
                      } else if (index == 1) {
                        return const Divider();
                      } else {
                        final warehouse = _filteredWarehouses[index - 2];
                        return ListTile(
                          leading: Container(
                            padding: const EdgeInsets.all(8.0),
                            decoration: BoxDecoration(
                              color: _getWarehouseColor(warehouse.type)
                                  .withValues(alpha: 0.1),
                              borderRadius: BorderRadius.circular(8.0),
                            ),
                            child: Icon(
                              _getWarehouseIcon(warehouse.type),
                              color: _getWarehouseColor(warehouse.type),
                              size: 20,
                            ),
                          ),
                          title: Text(warehouse.name),
                          subtitle: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              if (warehouse.address != null)
                                Text(warehouse.address!),
                              Text(
                                _getWarehouseTypeLabel(warehouse.type),
                                style: TextStyle(
                                  color: _getWarehouseColor(warehouse.type),
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ],
                          ),
                          onTap: () => Navigator.of(context).pop(warehouse),
                        );
                      }
                    },
                  ),
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Отмена'),
            ),
          ],
        ),
      ),
    );

    if (result != null || result == null) {
      _updateDistribution(
        productIndex,
        distributionIndex,
        distribution.copyWith(warehouseId: result?.id),
      );
      controller.clear();
      _filterWarehouses('', distributionKey);
    }
  }

  Widget _buildWarehouseSelector(
    int productIndex,
    int distributionIndex,
    Distribution distribution,
  ) {
    final distributionKey = _getDistributionKey(
      productIndex,
      distributionIndex,
    );

    Warehouse? selectedWarehouse;
    if (distribution.warehouseId != null) {
      try {
        selectedWarehouse = _warehouses.firstWhere(
          (w) => w.id == distribution.warehouseId,
        );
      } catch (e) {
        selectedWarehouse = null;
      }
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        GestureDetector(
          onTap: () => _showWarehouseSearchDialog(
            context,
            productIndex,
            distributionIndex,
            distribution,
            distributionKey,
          ),
          child: Container(
            padding:
                const EdgeInsets.symmetric(horizontal: 12.0, vertical: 16.0),
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey),
              borderRadius: BorderRadius.circular(4.0),
            ),
            child: Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Склад',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                              color: Colors.grey[600],
                            ),
                      ),
                      const SizedBox(height: 4.0),
                      Row(
                        children: [
                          if (selectedWarehouse != null)
                            Icon(
                              _getWarehouseIcon(selectedWarehouse.type),
                              size: 16,
                              color: _getWarehouseColor(selectedWarehouse.type),
                            ),
                          if (selectedWarehouse != null)
                            const SizedBox(width: 8.0),
                          Expanded(
                            child: Text(
                              selectedWarehouse?.name ?? 'Не выбран',
                              style: Theme.of(context).textTheme.bodyMedium,
                            ),
                          ),
                        ],
                      ),
                      if (selectedWarehouse?.address != null)
                        Text(
                          selectedWarehouse!.address!,
                          style:
                              Theme.of(context).textTheme.bodySmall?.copyWith(
                                    color: Colors.grey[600],
                                  ),
                        ),
                      if (selectedWarehouse != null)
                        Text(
                          _getWarehouseTypeLabel(selectedWarehouse.type),
                          style: Theme.of(context)
                              .textTheme
                              .bodySmall
                              ?.copyWith(
                                color:
                                    _getWarehouseColor(selectedWarehouse.type),
                                fontWeight: FontWeight.w500,
                              ),
                        ),
                    ],
                  ),
                ),
                const Icon(Icons.arrow_drop_down),
              ],
            ),
          ),
        ),
      ],
    );
  }

  IconData _getWarehouseIcon(WarehouseType type) {
    switch (type) {
      case WarehouseType.general:
        return Icons.warehouse;
      case WarehouseType.contractor:
        return Icons.business;
      case WarehouseType.virtualProject:
        return Icons.cloud_outlined;
    }
  }

  Color _getWarehouseColor(WarehouseType type) {
    switch (type) {
      case WarehouseType.general:
        return Colors.blue;
      case WarehouseType.contractor:
        return Colors.green;
      case WarehouseType.virtualProject:
        return Colors.purple;
    }
  }

  String _getWarehouseTypeLabel(WarehouseType type) {
    switch (type) {
      case WarehouseType.general:
        return 'Общий склад';
      case WarehouseType.contractor:
        return 'Склад контрагента';
      case WarehouseType.virtualProject:
        return 'Виртуальный склад проекта';
    }
  }
}
