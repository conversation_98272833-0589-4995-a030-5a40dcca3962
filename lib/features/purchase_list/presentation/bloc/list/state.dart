part of 'bloc.dart';

// In state.dart
@freezed
class BlocPurchaseListState with _$BlocPurchaseListState {
  const BlocPurchaseListState._();
  const factory BlocPurchaseListState({
    @Default(false) bool selecting,
    @Default({}) Map<String, ProvisionItemModel> selectedProducts,
    @Default(ProvisionsListModel()) ProvisionsListModel productItems,
    // @Default(null) ProvisionsFilter? selectedFilter,
    @Default([]) List<ProvisionProductModel> products,
    @Default({}) Map<String, bool> columnVisibility,
    SearchFiltersModel? searchFilters,
    @Default(false) bool isFiltersOpen,
    @Default(false) bool isLoading,
    @Default(0) int totalItems,
    // New field for column options
    @Default(ProvisionsColumnOptionsOutput())
    ProvisionsColumnOptionsOutput options,
    @Default(null) String? error,
    // Fields for task creation with provision items
    // @Default([]) List<ProvisionItemModel> selectedProvisionItemsForTask,
    @Default([]) List<ProvisionProductModel> selectedProvisionProductsForTask,
    // Fields for task progress management
    @Default([]) List<TaskProgressModel> selectedProgressTasks,
  }) = _BlocPurchaseListState;
}
