part of 'bloc.dart';

sealed class ProductionEvents {}

// Production Items Events
final class LoadProductionItems extends ProductionEvents {
  final String projectId;
  LoadProductionItems(this.projectId);
}

final class RefreshProductionItems extends ProductionEvents {}

// Production Tasks Events
final class LoadProductionTasks extends ProductionEvents {
  final ProductionTasksRequest request;
  LoadProductionTasks(this.request);
}

final class CreateProductionTask extends ProductionEvents {
  final ProductionTaskCreateRequest request;
  CreateProductionTask(this.request);
}

final class CancelProductionTask extends ProductionEvents {
  final ProductionTaskCancelRequest request;
  CancelProductionTask(this.request);
}

final class UpdateTaskStatus extends ProductionEvents {
  final ProductionTaskUpdateStatusRequest request;
  UpdateTaskStatus(this.request);
}

final class LoadTaskDetails extends ProductionEvents {
  final String taskId;
  LoadTaskDetails(this.taskId);
}

// UI State Events
final class SetSelectedItems extends ProductionEvents {
  final Set<String> selectedItemIds;
  SetSelectedItems(this.selectedItemIds);
}

final class ToggleItemSelection extends ProductionEvents {
  final String itemId;
  ToggleItemSelection(this.itemId);
}

final class ClearSelections extends ProductionEvents {}

final class SetCurrentView extends ProductionEvents {
  final ProductionView view;
  SetCurrentView(this.view);
}

final class SetStatusFilter extends ProductionEvents {
  final ProductionTaskStatus? status;
  SetStatusFilter(this.status);
}

final class SetDateFilter extends ProductionEvents {
  final DateTime? dateFrom;
  final DateTime? dateTo;
  SetDateFilter({this.dateFrom, this.dateTo});
}

final class ResetMessages extends ProductionEvents {}

enum ProductionView { items, tasks }
