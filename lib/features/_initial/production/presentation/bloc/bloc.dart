import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:sphere/features/_initial/production/data/models/index.dart';
import 'package:sphere/features/_initial/production/data/repositories/index.dart';

part 'bloc.freezed.dart';
part 'events.dart';
part 'state.dart';

class ProductionBloc extends Bloc<ProductionEvents, ProductionState> {
  ProductionBloc() : super(const ProductionState()) {
    // Production Items Events
    on<LoadProductionItems>((event, emit) async {
      emit(state.copyWith(
        isLoadingItems: true,
        itemsError: null,
        projectId: event.projectId,
      ));

      try {
        final response = await ProductionRepository.getItems(
          ProductionItemsRequest(projectId: event.projectId),
        );

        if (response?.statusCode == 200) {
          emit(state.copyWith(
            items: response?.data?.items ?? [],
            isLoadingItems: false,
          ));
        } else {
          emit(state.copyWith(
            isLoadingItems: false,
            itemsError: 'Ошибка загрузки элементов производства',
          ));
        }
      } catch (e) {
        emit(state.copyWith(
          isLoadingItems: false,
          itemsError: e.toString(),
        ));
      }
    });

    on<RefreshProductionItems>((event, emit) {
      if (state.projectId != null) {
        add(LoadProductionItems(state.projectId!));
      }
    });

    // Production Tasks Events
    on<LoadProductionTasks>((event, emit) async {
      emit(state.copyWith(
        isLoadingTasks: true,
        tasksError: null,
      ));

      try {
        final response = await ProductionRepository.getTasks(event.request);

        if (response?.statusCode == 200) {
          emit(state.copyWith(
            tasks: response?.data?.items ?? [],
            isLoadingTasks: false,
          ));
        } else {
          emit(state.copyWith(
            isLoadingTasks: false,
            tasksError: 'Ошибка загрузки заданий',
          ));
        }
      } catch (e) {
        emit(state.copyWith(
          isLoadingTasks: false,
          tasksError: e.toString(),
        ));
      }
    });

    on<CreateProductionTask>((event, emit) async {
      emit(state.copyWith(
        isCreatingTask: true,
        createTaskError: null,
        successMessage: null,
      ));

      try {
        final response = await ProductionRepository.createTask(event.request);

        if (response?.statusCode == 200) {
          emit(state.copyWith(
            isCreatingTask: false,
            successMessage: 'Задание создано успешно',
          ));

          // Refresh tasks list
          add(LoadProductionTasks(ProductionTasksRequest(
            projectId: event.request.projectId,
            status: state.statusFilter,
            dateFrom: state.dateFromFilter,
            dateTo: state.dateToFilter,
          )));
        } else {
          emit(state.copyWith(
            isCreatingTask: false,
            createTaskError: 'Ошибка создания задания',
          ));
        }
      } catch (e) {
        emit(state.copyWith(
          isCreatingTask: false,
          createTaskError: e.toString(),
        ));
      }
    });

    on<CancelProductionTask>((event, emit) async {
      emit(state.copyWith(
        isCancellingTask: true,
        cancelTaskError: null,
        successMessage: null,
      ));

      try {
        final response = await ProductionRepository.cancelTask(event.request);

        if (response?.statusCode == 200) {
          emit(state.copyWith(
            isCancellingTask: false,
            successMessage: response?.data?.message ?? 'Задание отменено',
          ));

          // Refresh tasks list
          add(LoadProductionTasks(ProductionTasksRequest(
            projectId: state.projectId,
            status: state.statusFilter,
            dateFrom: state.dateFromFilter,
            dateTo: state.dateToFilter,
          )));
        } else {
          emit(state.copyWith(
            isCancellingTask: false,
            cancelTaskError: 'Ошибка отмены задания',
          ));
        }
      } catch (e) {
        emit(state.copyWith(
          isCancellingTask: false,
          cancelTaskError: e.toString(),
        ));
      }
    });

    on<UpdateTaskStatus>((event, emit) async {
      emit(state.copyWith(
        isUpdatingStatus: true,
        updateStatusError: null,
        successMessage: null,
      ));

      try {
        final response =
            await ProductionRepository.updateTaskStatus(event.request);

        if (response?.statusCode == 200) {
          emit(state.copyWith(
            isUpdatingStatus: false,
            successMessage: 'Статус обновлен успешно',
          ));

          // Refresh tasks list
          add(LoadProductionTasks(ProductionTasksRequest(
            projectId: state.projectId,
            status: state.statusFilter,
            dateFrom: state.dateFromFilter,
            dateTo: state.dateToFilter,
          )));
        } else {
          emit(state.copyWith(
            isUpdatingStatus: false,
            updateStatusError: 'Ошибка обновления статуса',
          ));
        }
      } catch (e) {
        emit(state.copyWith(
          isUpdatingStatus: false,
          updateStatusError: e.toString(),
        ));
      }
    });

    on<LoadTaskDetails>((event, emit) async {
      emit(state.copyWith(
        isLoadingTaskDetails: true,
        taskDetailsError: null,
      ));

      try {
        final response = await ProductionRepository.getTaskDetails(
          ProductionTaskDetailsRequest(taskId: event.taskId),
        );

        if (response?.statusCode == 200) {
          emit(state.copyWith(
            selectedTaskDetails: response?.data,
            isLoadingTaskDetails: false,
          ));
        } else {
          emit(state.copyWith(
            isLoadingTaskDetails: false,
            taskDetailsError: 'Ошибка загрузки деталей задания',
          ));
        }
      } catch (e) {
        emit(state.copyWith(
          isLoadingTaskDetails: false,
          taskDetailsError: e.toString(),
        ));
      }
    });

    // UI State Events
    on<SetSelectedItems>((event, emit) {
      emit(state.copyWith(selectedItemIds: event.selectedItemIds));
    });

    on<ToggleItemSelection>((event, emit) {
      final newSelection = Set<String>.from(state.selectedItemIds);
      if (newSelection.contains(event.itemId)) {
        newSelection.remove(event.itemId);
      } else {
        newSelection.add(event.itemId);
      }
      emit(state.copyWith(selectedItemIds: newSelection));
    });

    on<ClearSelections>((event, emit) {
      emit(state.copyWith(selectedItemIds: {}));
    });

    on<SetCurrentView>((event, emit) {
      emit(state.copyWith(currentView: event.view));
    });

    on<SetStatusFilter>((event, emit) {
      emit(state.copyWith(statusFilter: event.status));

      // Reload tasks with new filter
      if (state.currentView == ProductionView.tasks) {
        add(LoadProductionTasks(ProductionTasksRequest(
          projectId: state.projectId,
          status: event.status,
          dateFrom: state.dateFromFilter,
          dateTo: state.dateToFilter,
        )));
      }
    });

    on<SetDateFilter>((event, emit) {
      emit(state.copyWith(
        dateFromFilter: event.dateFrom,
        dateToFilter: event.dateTo,
      ));

      // Reload tasks with new filter
      if (state.currentView == ProductionView.tasks) {
        add(LoadProductionTasks(ProductionTasksRequest(
          projectId: state.projectId,
          status: state.statusFilter,
          dateFrom: event.dateFrom,
          dateTo: event.dateTo,
        )));
      }
    });

    on<ResetMessages>((event, emit) {
      emit(state.copyWith(
        successMessage: null,
        itemsError: null,
        tasksError: null,
        taskDetailsError: null,
        createTaskError: null,
        cancelTaskError: null,
        updateStatusError: null,
      ));
    });
  }
}
