import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:intl/intl.dart';
import 'package:sphere/features/_initial/production/data/models/index.dart';
import 'package:sphere/features/_initial/production/presentation/bloc/bloc.dart';
import 'package:sphere/shared/styles/colors.dart';
import 'package:sphere/shared/styles/fonts.dart';
import 'package:sphere/shared/widgets/interactive/ghost_button.dart';

class TaskDetailsDialog extends StatelessWidget {
  final String taskId;

  const TaskDetailsDialog({
    super.key,
    required this.taskId,
  });

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ProductionBloc, ProductionState>(
      builder: (context, state) {
        if (state.isLoadingTaskDetails) {
          return const Center(child: CircularProgressIndicator());
        }

        if (state.taskDetailsError != null) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  'Ошибка загрузки',
                  style: Fonts.headlineSmall,
                ),
                const SizedBox(height: 8),
                Text(
                  state.taskDetailsError!,
                  style: Fonts.bodyMedium.copyWith(color: AppColors.lightError),
                ),
                const SizedBox(height: 16),
                GhostButton(
                  onTap: () => Navigator.of(context).pop(),
                  child: const Padding(
                    padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                    child: Text('Закрыть'),
                  ),
                ),
              ],
            ),
          );
        }

        final task = state.selectedTaskDetails;
        if (task == null) {
          return const Center(child: Text('Задание не найдено'));
        }

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Детали задания',
                  style: Fonts.headlineSmall,
                ),
                GhostButton(
                  onTap: () => Navigator.of(context).pop(),
                  child: const Icon(Icons.close),
                ),
              ],
            ),
            const SizedBox(height: 24),
            Expanded(
              child: SingleChildScrollView(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildInfoSection('Основная информация', [
                      _buildInfoRow('ID задания', task.id ?? 'N/A'),
                      _buildInfoRow('ID проекта', task.projectId ?? 'N/A'),
                      _buildInfoRow('ID продукта', task.productId ?? 'N/A'),
                      _buildInfoRow('Количество',
                          task.quantityRequired?.toString() ?? 'N/A'),
                      _buildInfoRow(
                          'Статус', task.status?.getName() ?? 'Неизвестно'),
                    ]),
                    const SizedBox(height: 24),
                    if (task.materials?.isNotEmpty ?? false) ...[
                      _buildMaterialsSection(task.materials!),
                      const SizedBox(height: 24),
                    ],
                    if (task.statusHistory?.isNotEmpty ?? false) ...[
                      _buildStatusHistorySection(task.statusHistory!),
                      const SizedBox(height: 24),
                    ],
                    _buildInfoSection('Временные метки', [
                      _buildInfoRow(
                        'Создано',
                        task.createdAt != null
                            ? DateFormat('dd.MM.yyyy HH:mm:ss')
                                .format(task.createdAt!)
                            : 'N/A',
                      ),
                      _buildInfoRow(
                        'Обновлено',
                        task.updatedAt != null
                            ? DateFormat('dd.MM.yyyy HH:mm:ss')
                                .format(task.updatedAt!)
                            : 'N/A',
                      ),
                    ]),
                    if (task.cancelled == true) ...[
                      const SizedBox(height: 24),
                      _buildInfoSection('Информация об отмене', [
                        // TODO:
                        // _buildInfoRow('Отменено пользователем', task.cancelledBy ?? 'N/A'),
                        _buildInfoRow(
                          'Дата отмены',
                          task.cancelledAt != null
                              ? DateFormat('dd.MM.yyyy HH:mm:ss')
                                  .format(task.cancelledAt!)
                              : 'N/A',
                        ),
                        _buildInfoRow('Причина отмены',
                            task.cancelReason ?? 'Не указана'),
                      ]),
                    ],
                  ],
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  Widget _buildInfoSection(String title, List<Widget> children) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: Fonts.titleMedium,
        ),
        const SizedBox(height: 12),
        Container(
          width: double.infinity,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            border: Border.all(color: Colors.grey.shade300),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: children,
          ),
        ),
      ],
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              '$label:',
              style: Fonts.labelMedium,
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: Fonts.bodyMedium,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMaterialsSection(List<ProductionTaskMaterialModel> materials) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Материалы',
          style: Fonts.titleMedium,
        ),
        const SizedBox(height: 12),
        Container(
          width: double.infinity,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            border: Border.all(color: Colors.grey.shade300),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Column(
            children: materials
                .map((material) => _buildMaterialRow(material))
                .toList(),
          ),
        ),
      ],
    );
  }

  Widget _buildMaterialRow(ProductionTaskMaterialModel material) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        children: [
          Expanded(
            flex: 2,
            child: Text(
              material.materialName ?? 'Неизвестный материал',
              style: Fonts.labelMedium,
            ),
          ),
          Expanded(
            child: Text(
              'Требуется: ${material.requiredQuantity?.toStringAsFixed(2) ?? '0'}',
              style: Fonts.bodySmall,
            ),
          ),
          Expanded(
            child: Text(
              'Выдано: ${material.issuedQuantity?.toStringAsFixed(2) ?? '0'}',
              style: Fonts.bodySmall,
            ),
          ),
          Expanded(
            child: Text(
              material.materialUnit ?? '',
              style: Fonts.bodySmall,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatusHistorySection(
      List<ProductionTaskStatusHistoryModel> history) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'История статусов',
          style: Fonts.titleMedium,
        ),
        const SizedBox(height: 12),
        Container(
          width: double.infinity,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            border: Border.all(color: Colors.grey.shade300),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Column(
            children: history.map((item) => _buildHistoryRow(item)).toList(),
          ),
        ),
      ],
    );
  }

  Widget _buildHistoryRow(ProductionTaskStatusHistoryModel item) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        children: [
          Expanded(
            flex: 2,
            child: Text(
              item.status?.getName() ?? 'Неизвестно',
              style: Fonts.labelMedium,
            ),
          ),
          Expanded(
            flex: 2,
            child: Text(
              item.date != null
                  ? DateFormat('dd.MM.yyyy HH:mm').format(item.date!)
                  : 'N/A',
              style: Fonts.bodySmall,
            ),
          ),
          Expanded(
            child: Text(
              item.userId ?? 'N/A',
              style: Fonts.bodySmall,
            ),
          ),
          if (item.comment?.isNotEmpty ?? false) ...[
            Expanded(
              flex: 2,
              child: Text(
                item.comment!,
                style: Fonts.bodySmall,
              ),
            ),
          ],
        ],
      ),
    );
  }
}
