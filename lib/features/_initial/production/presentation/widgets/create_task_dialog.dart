import 'package:flutter/material.dart';
import 'package:sphere/features/_initial/production/data/models/index.dart';
import 'package:sphere/shared/styles/fonts.dart';
import 'package:sphere/shared/widgets/interactive/elevated_button.dart';
import 'package:sphere/shared/widgets/interactive/ghost_button.dart';

class CreateTaskDialog extends StatefulWidget {
  final String projectId;
  final Function(ProductionTaskCreateRequest request) onTaskCreated;

  const CreateTaskDialog({
    super.key,
    required this.projectId,
    required this.onTaskCreated,
  });

  @override
  State<CreateTaskDialog> createState() => _CreateTaskDialogState();
}

class _CreateTaskDialogState extends State<CreateTaskDialog> {
  final _formKey = GlobalKey<FormState>();
  final _productIdController = TextEditingController();
  final _quantityController = TextEditingController(text: '1.0');

  @override
  void dispose() {
    _productIdController.dispose();
    _quantityController.dispose();
    super.dispose();
  }

  void _createTask() {
    if (_formKey.currentState?.validate() ?? false) {
      final request = ProductionTaskCreateRequest(
        projectId: widget.projectId,
        productId: _productIdController.text.trim(),
        quantityRequired: double.tryParse(_quantityController.text) ?? 1.0,
      );

      widget.onTaskCreated(request);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Form(
      key: _formKey,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Создать производственное задание',
            style: Fonts.headlineSmall,
          ),
          const SizedBox(height: 24),
          TextFormField(
            controller: _productIdController,
            decoration: const InputDecoration(
              labelText: 'ID продукта *',
              hintText: 'Введите ID продукта',
              border: OutlineInputBorder(),
            ),
            validator: (value) {
              if (value == null || value.trim().isEmpty) {
                return 'Поле обязательно для заполнения';
              }
              return null;
            },
          ),
          const SizedBox(height: 16),
          TextFormField(
            controller: _quantityController,
            decoration: const InputDecoration(
              labelText: 'Количество *',
              hintText: 'Введите количество',
              border: OutlineInputBorder(),
            ),
            keyboardType: TextInputType.number,
            validator: (value) {
              if (value == null || value.trim().isEmpty) {
                return 'Поле обязательно для заполнения';
              }
              final quantity = double.tryParse(value);
              if (quantity == null || quantity <= 0) {
                return 'Введите корректное положительное число';
              }
              return null;
            },
          ),
          const SizedBox(height: 16),
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.surface,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: Theme.of(context).dividerColor,
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Информация о проекте',
                  style: Fonts.labelMedium,
                ),
                const SizedBox(height: 8),
                Text(
                  'ID проекта: ${widget.projectId}',
                  style: Fonts.bodySmall.copyWith(
                    color: Theme.of(context).textTheme.bodySmall?.color,
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 24),
          Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              GhostButton(
                onTap: () => Navigator.of(context).pop(),
                child: const Padding(
                  padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  child: Text('Отмена'),
                ),
              ),
              const SizedBox(width: 12),
              CustomElevatedButton(
                onPressed: _createTask,
                text: 'Создать задание',
              ),
            ],
          ),
        ],
      ),
    );
  }
}
