import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:sphere/features/_initial/bloc/bloc.dart';
import 'package:sphere/features/_initial/production/data/models/index.dart';
import 'package:sphere/features/_initial/production/presentation/bloc/bloc.dart';
import 'package:sphere/features/_initial/production/presentation/widgets/cancel_task_dialog.dart';
import 'package:sphere/features/_initial/production/presentation/widgets/create_task_dialog.dart';
import 'package:sphere/features/_initial/production/presentation/widgets/production_items_view.dart';
import 'package:sphere/features/_initial/production/presentation/widgets/production_tasks_view.dart';
import 'package:sphere/features/_initial/production/presentation/widgets/task_details_dialog.dart';
import 'package:sphere/features/_initial/production/presentation/widgets/update_status_dialog.dart';
import 'package:sphere/features/project/data/models/project.dart';
import 'package:sphere/features/project/data/repositories/index.dart';
import 'package:sphere/shared/data/models/search.dart';
import 'package:sphere/shared/styles/colors.dart';
import 'package:sphere/shared/styles/fonts.dart';
import 'package:sphere/shared/widgets/containers/popup/index.dart';
import 'package:sphere/shared/widgets/interactive/elevated_button.dart';
import 'package:sphere/shared/widgets/overlay/app_bar/config.dart';

@RoutePage()
class ProductionScreen extends StatefulWidget {
  const ProductionScreen({super.key});

  @override
  State<ProductionScreen> createState() => _ProductionScreenState();
}

class _ProductionScreenState extends State<ProductionScreen>
    with AutoRouteAwareStateMixin<ProductionScreen> {
  late ProductionBloc _productionBloc;
  String? _selectedProjectId;

  List<ProjectModel> _projects = [];
  bool _isLoadingProjects = false;

  @override
  void initState() {
    super.initState();
    _productionBloc = ProductionBloc();
    _loadProjects();
  }

  @override
  void dispose() {
    _productionBloc.close();
    super.dispose();
  }

  Future<void> _loadProjects() async {
    if (!mounted) return;

    setState(() {
      _isLoadingProjects = true;
    });

    try {
      final result = await ProjectRepository.search(SearchModel(
        filters: SearchFiltersModel(
          branchId: '67405bd1f61fcbeb284d4eb5',
        ),
      ));

      if (mounted && result?.data != null && result!.data?.items != null) {
        setState(() {
          _projects = result.data!.items!;
          _isLoadingProjects = false;
        });
      } else if (mounted) {
        setState(() {
          _projects = [];
          _isLoadingProjects = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _projects = [];
          _isLoadingProjects = false;
        });
      }
    }
  }

  void _onProjectSelected(ProjectModel project) {
    setState(() {
      _selectedProjectId = project.id;
    });

    if (project.id != null) {
      _productionBloc.add(LoadProductionItems(project.id!));
    }
  }

  void _showSnackBar(String message, {bool isError = false}) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: isError ? AppColors.lightError : null,
        ),
      );
      // reset
      _productionBloc.add(ResetMessages());
    }
  }

  void _showCreateTaskDialog() {
    if (_selectedProjectId == null) return;

    showBaseDialog(
      context,
      maxWidth: 600,
      builder: (context) => CreateTaskDialog(
        projectId: _selectedProjectId!,
        onTaskCreated: (request) {
          _productionBloc.add(CreateProductionTask(request));
          Navigator.of(context).pop();
        },
      ),
    );
  }

  void _showTaskDetails(String taskId) {
    _productionBloc.add(LoadTaskDetails(taskId));

    showBaseDialog(
      context,
      maxWidth: 800,
      builder: (context) => BlocProvider.value(
        value: _productionBloc,
        child: TaskDetailsDialog(taskId: taskId),
      ),
    );
  }

  void _showUpdateStatusDialog(ProductionTaskModel task) {
    showBaseDialog(
      context,
      maxWidth: 500,
      builder: (context) => UpdateStatusDialog(
        task: task,
        onStatusUpdated: (request) {
          _productionBloc.add(UpdateTaskStatus(request));
          Navigator.of(context).pop();
        },
      ),
    );
  }

  void _showCancelTaskDialog(ProductionTaskModel task) {
    showBaseDialog(
      context,
      maxWidth: 500,
      builder: (context) => CancelTaskDialog(
        task: task,
        onTaskCancelled: (request) {
          _productionBloc.add(CancelProductionTask(request));
          Navigator.of(context).pop();
        },
      ),
    );
  }

  @override
  void didInitTabRoute(TabPageRoute? previousRoute) async {
    _setAppBarConfig();
    _loadProjects();
  }

  @override
  void didChangeTabRoute(TabPageRoute previousRoute) async {
    _setAppBarConfig();
    _loadProjects();
  }

  void _setAppBarConfig() {
    // final isMobile = MediaQuery.of(context).size.width < 600;

    final newConfig = CustomAppBarConfig(
      title: 'Производство',
      height: 48.0,
    );

    context.read<BlocInitial>().add(SetAppBarConfig(newConfig));
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider.value(
      value: _productionBloc,
      child: BlocListener<ProductionBloc, ProductionState>(
        listener: (context, state) {
          if (state.successMessage != null) {
            _showSnackBar(state.successMessage!);
          }

          if (state.itemsError != null) {
            _showSnackBar(state.itemsError!, isError: true);
          }

          if (state.tasksError != null) {
            _showSnackBar(state.tasksError!, isError: true);
          }

          if (state.createTaskError != null) {
            _showSnackBar(state.createTaskError!, isError: true);
          }

          if (state.cancelTaskError != null) {
            _showSnackBar(state.cancelTaskError!, isError: true);
          }

          if (state.updateStatusError != null) {
            _showSnackBar(state.updateStatusError!, isError: true);
          }
        },
        child: _buildBody(),
      ),
    );
  }

  Widget _buildBody() {
    return SafeArea(
      child: Column(
        children: [
          SizedBox(height: 20.0),
          _buildProjectSelector(),
          const SizedBox(height: 16),
          if (_selectedProjectId != null) ...[
            Expanded(
              child: _buildProductionContent(),
            ),
          ] else ...[
            const Expanded(
              child: Center(
                child: Text(
                  'Выберите проект для работы с производством',
                  style: TextStyle(fontSize: 18),
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildProjectSelector() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 20),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        border: Border.all(color: AppColors.lightStroke),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        children: [
          const Text(
            'Проект:',
            style: TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: _isLoadingProjects
                ? const Center(child: CircularProgressIndicator())
                : _projects.isEmpty
                    ? Container(
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          border:
                              Border.all(color: Theme.of(context).dividerColor),
                          borderRadius: BorderRadius.circular(4),
                        ),
                        child: const Text('Нет доступных проектов'),
                      )
                    : DropdownButtonFormField<String>(
                        value: _selectedProjectId,
                        decoration: const InputDecoration(
                          hintText: 'Выберите проект',
                          border: OutlineInputBorder(),
                          contentPadding:
                              EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                        ),
                        items: _projects.map((project) {
                          return DropdownMenuItem<String>(
                            value: project.id,
                            child: Row(
                              children: [
                                Text(
                                  project.name ?? 'Без названия',
                                  overflow: TextOverflow.ellipsis,
                                ),
                                if (project.number != null) ...[
                                  const SizedBox(width: 8),
                                  Text(
                                    '(${project.number})',
                                    style: TextStyle(
                                      color: Theme.of(context)
                                          .textTheme
                                          .bodySmall
                                          ?.color,
                                      fontSize: 12,
                                    ),
                                  ),
                                ],
                              ],
                            ),
                          );
                        }).toList(),
                        onChanged: (projectId) {
                          if (projectId != null) {
                            final project =
                                _projects.firstWhere((p) => p.id == projectId);
                            _onProjectSelected(project);
                          }
                        },
                      ),
          ),
        ],
      ),
    );
  }

  Widget _buildProductionContent() {
    return BlocBuilder<ProductionBloc, ProductionState>(
      builder: (context, state) {
        return LayoutBuilder(
          builder: (context, constraints) {
            if (constraints.maxHeight <= 0) {
              return const SizedBox.shrink();
            }

            return Column(
              children: [
                _buildViewTabs(state),
                const SizedBox(height: 16),
                Expanded(
                  child: _buildCurrentView(state),
                ),
              ],
            );
          },
        );
      },
    );
  }

  Widget _buildViewTabs(ProductionState state) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 20),
      child: Row(
        children: [
          _buildTabButton(
            'Элементы производства',
            ProductionView.items,
            state.currentView == ProductionView.items,
          ),
          const SizedBox(width: 12),
          _buildTabButton(
            'Задания',
            ProductionView.tasks,
            state.currentView == ProductionView.tasks,
          ),
          const Spacer(),
          if (state.currentView == ProductionView.tasks) ...[
            CustomElevatedButton(
              onPressed: _showCreateTaskDialog,
              text: 'Создать задание',
              style: Fonts.labelSmall,
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildTabButton(String title, ProductionView view, bool isSelected) {
    return GestureDetector(
      onTap: () {
        _productionBloc.add(SetCurrentView(view));
        if (view == ProductionView.tasks) {
          _productionBloc.add(LoadProductionTasks(ProductionTasksRequest(
            projectId: _selectedProjectId,
          )));
        }
      },
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        decoration: BoxDecoration(
          color: isSelected
              ? Theme.of(context).colorScheme.primary
              : Colors.transparent,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: isSelected
                ? Theme.of(context).colorScheme.primary
                : Theme.of(context).dividerColor,
          ),
        ),
        child: Text(
          title,
          style: Fonts.labelMedium.copyWith(
            color: isSelected
                ? Colors.white
                : Theme.of(context).textTheme.bodyMedium?.color,
          ),
        ),
      ),
    );
  }

  Widget _buildCurrentView(ProductionState state) {
    switch (state.currentView) {
      case ProductionView.items:
        return ProductionItemsView(
          items: state.items,
          isLoading: state.isLoadingItems,
          error: state.itemsError,
          onCreateTask: (productId, quantity) {
            final request = ProductionTaskCreateRequest(
              projectId: _selectedProjectId,
              productId: productId,
              quantityRequired: quantity,
            );
            _productionBloc.add(CreateProductionTask(request));
          },
          onUpdateStatus: _showUpdateStatusDialog,
        );
      case ProductionView.tasks:
        return ProductionTasksView(
          tasks: state.tasks,
          isLoading: state.isLoadingTasks,
          error: state.tasksError,
          onTaskTap: _showTaskDetails,
          onUpdateStatus: _showUpdateStatusDialog,
          onCancelTask: _showCancelTaskDialog,
          onFilterChanged: (status, dateFrom, dateTo) {
            _productionBloc.add(SetStatusFilter(status));
            _productionBloc.add(SetDateFilter(
              dateFrom: dateFrom,
              dateTo: dateTo,
            ));
          },
        );
    }
  }
}
