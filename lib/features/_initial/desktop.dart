import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:sphere/core/gen/assets.gen.dart';
import 'package:sphere/core/navigation/index.gr.dart';
import 'package:sphere/features/_initial/app_bar.dart';
import 'package:sphere/features/_initial/bloc/bloc.dart';
import 'package:sphere/shared/widgets/overlay/bottom_navigation/navigation_button.dart';
import 'package:sphere/shared/widgets/overlay/navigation_rail/index.dart';
import 'package:sphere/shared/widgets/svg/index.dart';

class DesktopLayout extends StatefulWidget {
  const DesktopLayout({super.key});

  @override
  State<DesktopLayout> createState() => _DesktopLayoutState();
}

class _DesktopLayoutState extends State<DesktopLayout> {
  bool isExtend = true;

  void toggleExtend() {
    setState(() {
      isExtend = !isExtend;
    });
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<BlocInitial, BlocInitialState>(
        builder: (context, state) {
      return AutoTabsRouter(
        // lazyLoad: true,
        routes: [
          NomenclaturesRoute(),
          StorageRoute(),
          WarehousesRoute(),
          OtkDeliveriesRoute(),
          OtkDefectActsRoute(),
          ProductionRoute(),
          // DeliveriesRoute(),
          BranchRoute(),
          NotificationRoute(),
          SettingsRoute(),
        ],
        builder: (context, child) {
          final currentIndex = AutoTabsRouter.of(context).activeIndex;

          return Scaffold(
            appBar: InitialAppBar(config: state.config),
            body: Row(
              children: [
                CustomNavigationRail(
                  isExtended: isExtend,
                  toggleExtend: toggleExtend,
                  currentIndex: currentIndex,
                  onTap: (index) {
                    AutoTabsRouter.of(context).setActiveIndex(index);
                  },
                  children: [
                    CustomNavigationButton(
                      isActive: currentIndex == 0,
                      icon: SVG(Assets.icons.developerGuide, width: 29.0),
                      label: 'Номенклатуры',
                    ),
                    CustomNavigationButton(
                      isActive: currentIndex == 1,
                      icon: SVG(Assets.icons.square, width: 29.0),
                      label: 'Склад',
                    ),
                    CustomNavigationButton(
                      isActive: currentIndex == 2,
                      icon: SVG(Assets.icons.warehouse, width: 29.0),
                      label: 'Склады',
                    ),
                    CustomNavigationButton(
                      isActive: currentIndex == 3,
                      icon: SVG(Assets.icons.check, width: 29.0),
                      label: 'ТК, Поставки',
                    ),
                    CustomNavigationButton(
                      isActive: currentIndex == 4,
                      icon: SVG(Assets.icons.description, width: 29.0),
                      label: 'Акты на брак',
                    ),
                    CustomNavigationButton(
                      isActive: currentIndex == 5,
                      icon: SVG(Assets.icons.settings, width: 29.0),
                      label: 'Производство',
                    ),
                    // CustomNavigationButton(
                    //   isActive: currentIndex == 3,
                    //   icon: SVG(Assets.icons.pallet, width: 29.0),
                    //   label: 'Поставки',
                    // ),
                    CustomNavigationButton(
                      isActive: currentIndex == 6,
                      icon: SVG(Assets.icons.home, width: 29.0),
                      label: 'Главный экран',
                    ),
                    CustomNavigationButton(
                      isActive: currentIndex == 7,
                      icon: SVG(Assets.icons.notifications, width: 29.0),
                      // withNotification: true,
                      label: 'Уведомления',
                    ),
                    CustomNavigationButton(
                      isActive: currentIndex == 8,
                      icon: SVG(Assets.icons.settings, width: 29.0),
                      label: 'Настройки',
                    ),
                  ],
                ),
                Expanded(child: child),
              ],
            ),
          );
        },
      );
    });
  }
}
